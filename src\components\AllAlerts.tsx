
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Alert<PERSON>riangle,
  CheckCircle,
  Clock,
  Bell,
  Filter,
  MapPin,
  Brain,
  Zap,
  TrendingUp,
  BarChart3,
  Building,
  Users,
  Milk,
  Thermometer,
  Settings,
  Truck
} from "lucide-react";
import DCSFilters from "@/components/filters/DCSFilters";
import DCSSelector from "@/components/filters/DCSSelector";
import { dcsData, filterDCSData, getDCSStats } from "@/data/dcsData";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Toolt<PERSON>,
  Legend,
  ArcElement
);

const AllAlerts = () => {
  const [filterType, setFilterType] = useState("all");
  const [filterDepartment, setFilterDepartment] = useState("all");
  const [selectedDCS, setSelectedDCS] = useState("all");
  const [selectedTaluka, setSelectedTaluka] = useState("all");
  const [selectedVillage, setSelectedVillage] = useState("all");
  const [selectedPerformance, setSelectedPerformance] = useState("all");
  const [viewMode, setViewMode] = useState("overview"); // overview, dcs-alerts, alert-analytics

  // Get filtered DCS data for alerts
  const getFilteredDCSData = () => {
    return filterDCSData({
      taluka: selectedTaluka,
      village: selectedVillage,
      performance: selectedPerformance
    });
  };

  // Generate DCS alerts data
  const getDCSAlertsData = () => {
    const filteredDCS = getFilteredDCSData();

    return filteredDCS.flatMap(dcs => {
      const alerts = [];

      // Generate alerts based on DCS performance and metrics
      if (dcs.qualityScore < 95) {
        alerts.push({
          id: `${dcs.id}-quality`,
          type: dcs.qualityScore < 90 ? "critical" : "warning",
          category: dcs.qualityScore < 90 ? "major" : "medium",
          title: `Quality Score Alert - ${dcs.name}`,
          description: `Quality score: ${dcs.qualityScore}% (Target: >95%)`,
          time: `${Math.floor(Math.random() * 60)} minutes ago`,
          department: "Quality Control",
          location: `${dcs.village}, ${dcs.taluka}`,
          dcsId: dcs.id,
          dcsName: dcs.name,
          aiAnalysis: `Quality metrics below threshold. Fat content: ${dcs.avgFatContent}%, SNF: ${dcs.avgSNFContent}%. Trend analysis shows ${Math.random() > 0.5 ? 'declining' : 'fluctuating'} quality over past week.`,
          recommendedAction: dcs.aiInsights.recommendation,
          impact: dcs.qualityScore < 90 ? "High - Product quality at risk" : "Medium - Quality monitoring required",
          status: "active"
        });
      }

      if (dcs.pendingPayments > 40000) {
        alerts.push({
          id: `${dcs.id}-payment`,
          type: dcs.pendingPayments > 60000 ? "critical" : "warning",
          category: dcs.pendingPayments > 60000 ? "major" : "medium",
          title: `Payment Overdue - ${dcs.name}`,
          description: `Pending payments: ₹${(dcs.pendingPayments / 1000).toFixed(0)}K`,
          time: `${Math.floor(Math.random() * 120)} minutes ago`,
          department: "Finance",
          location: `${dcs.village}, ${dcs.taluka}`,
          dcsId: dcs.id,
          dcsName: dcs.name,
          aiAnalysis: `Payment delay detected. Current outstanding: ₹${dcs.pendingPayments}. Cash flow analysis suggests potential liquidity issues.`,
          recommendedAction: "Immediate payment processing required. Contact DCS management for payment schedule.",
          impact: dcs.pendingPayments > 60000 ? "High - Farmer payment delays" : "Medium - Payment monitoring required",
          status: "active"
        });
      }

      if (dcs.dailyMilkCollection < 500) {
        alerts.push({
          id: `${dcs.id}-collection`,
          type: "info",
          category: "minor",
          title: `Low Collection Alert - ${dcs.name}`,
          description: `Daily collection: ${dcs.dailyMilkCollection}L (Below average)`,
          time: `${Math.floor(Math.random() * 180)} minutes ago`,
          department: "Operations",
          location: `${dcs.village}, ${dcs.taluka}`,
          dcsId: dcs.id,
          dcsName: dcs.name,
          aiAnalysis: `Collection volume below regional average. Seasonal factors and farmer participation analysis suggests potential for improvement.`,
          recommendedAction: "Engage with farmers to understand collection challenges. Consider incentive programs.",
          impact: "Low - Collection optimization opportunity",
          status: "active"
        });
      }

      // Random equipment alerts
      if (Math.random() > 0.7) {
        const equipmentTypes = ['Bulk Milk Cooler', 'Testing Equipment', 'Cold Storage', 'Generator'];
        const equipment = equipmentTypes[Math.floor(Math.random() * equipmentTypes.length)];
        alerts.push({
          id: `${dcs.id}-equipment`,
          type: Math.random() > 0.5 ? "warning" : "info",
          category: Math.random() > 0.5 ? "medium" : "minor",
          title: `Equipment Maintenance - ${dcs.name}`,
          description: `${equipment} requires scheduled maintenance`,
          time: `${Math.floor(Math.random() * 240)} minutes ago`,
          department: "Maintenance",
          location: `${dcs.village}, ${dcs.taluka}`,
          dcsId: dcs.id,
          dcsName: dcs.name,
          aiAnalysis: `Predictive maintenance algorithm indicates ${equipment} efficiency declining. Maintenance window recommended.`,
          recommendedAction: `Schedule maintenance for ${equipment} during low-activity hours.`,
          impact: "Medium - Preventive maintenance required",
          status: "active"
        });
      }

      return alerts;
    });
  };

  const handleClearFilters = () => {
    setSelectedDCS('all');
    setSelectedTaluka('all');
    setSelectedVillage('all');
    setSelectedPerformance('all');
  };

  const allAlerts = [
    {
      id: 1,
      type: "critical",
      category: "major",
      title: "Cold Storage Temperature Alert",
      description: "Cold Storage B: 6.2°C (Target: 4-5°C)",
      time: "2 minutes ago",
      department: "Energy Management",
      location: "Building A - Cold Storage Unit B",
      aiAnalysis: "Compressor efficiency down 15%, potential refrigerant leak detected. Temperature trend shows gradual increase over 30 minutes.",
      recommendedAction: "Immediate technician dispatch required. Check refrigerant levels and compressor functionality. Consider backup cooling activation.",
      impact: "High - Product quality at risk",
      status: "active"
    },
    {
      id: 2,
      type: "warning",
      category: "medium", 
      title: "Packaging Line Seal Defects",
      description: "Line 3 seal defect rate: 2.1% (Target: <1%)",
      time: "8 minutes ago",
      department: "Quality Control",
      location: "Production Floor B - Line 3",
      aiAnalysis: "Sealing temperature variance detected (±3°C), pressure inconsistency noted. Defect pattern suggests mechanical calibration issue.",
      recommendedAction: "Adjust sealing parameters to 180°C±1°C, calibrate pressure settings to 2.5 bar, inspect heating element condition.",
      impact: "Medium - Increased waste, quality concerns",
      status: "active"
    },
    {
      id: 3,
      type: "info",
      category: "minor",
      title: "Festival Season Demand Forecast",
      description: "Expected 15% increase in ghee demand for Diwali",
      time: "15 minutes ago",
      department: "Sales",
      location: "Planning Office",
      aiAnalysis: "Historical data shows consistent 12-18% spike during festival periods. Regional analysis indicates higher demand from Kini and Wathar routes.",
      recommendedAction: "Increase ghee production by 20% starting next week. Prepare additional 500ml and 1L packaging materials. Schedule extra shifts.",
      impact: "Opportunity - Revenue increase potential",
      status: "acknowledged"
    },
    {
      id: 4,
      type: "critical",
      category: "major",
      title: "Milk Quality SNF Anomaly",
      description: "SNF levels below 8.0% detected in Peth Vadgaon route",
      time: "1 hour ago",
      department: "Quality Control",
      location: "Quality Lab - Intake Section",
      aiAnalysis: "ML model detected unusual SNF pattern from 3 consecutive samples. Potential adulteration or feed quality issue identified.",
      recommendedAction: "Immediate re-testing required. Contact Peth Vadgaon collection center. Implement enhanced quality checks for this route.",
      impact: "High - Product quality and compliance risk",
      status: "active"
    },
    {
      id: 5,
      type: "warning",
      category: "medium",
      title: "Pasteurizer Maintenance Due",
      description: "Predictive maintenance alert for Unit 2",
      time: "2 hours ago",
      department: "Processing",
      location: "Processing Unit 2",
      aiAnalysis: "Sensor data indicates early wear indicators in pump bearings. Vibration patterns suggest maintenance within 48 hours.",
      recommendedAction: "Schedule preventive maintenance for next shutdown. Order replacement bearings. Plan production reallocation to Unit 1.",
      impact: "Medium - Potential downtime prevention",
      status: "scheduled"
    },
    {
      id: 6,
      type: "info",
      category: "minor",
      title: "Route Optimization Available",
      description: "18% fuel savings possible for Kolhapur district",
      time: "3 hours ago",
      department: "Dispatch",
      location: "Dispatch Control Room",
      aiAnalysis: "AI logistics model identified optimal routing combining Shiroli, Hamidwada, and Gajra stops in single trip.",
      recommendedAction: "Implement optimized route from tomorrow. Update driver schedules. Expected fuel saving: ₹2,400/week.",
      impact: "Cost saving opportunity",
      status: "pending"
    }
  ];

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'info': return <CheckCircle className="h-5 w-5 text-blue-600" />;
      default: return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'critical': return 'bg-red-500 text-white';
      case 'major': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-white';
      case 'minor': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-red-100 text-red-800';
      case 'acknowledged': return 'bg-yellow-100 text-yellow-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredAlerts = allAlerts.filter(alert => {
    const typeMatch = filterType === 'all' || alert.type === filterType;
    const deptMatch = filterDepartment === 'all' || alert.department.toLowerCase().includes(filterDepartment.toLowerCase());
    return typeMatch && deptMatch;
  });

  // Chart Data
  const alertTrendData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Critical Alerts',
        data: [3, 2, 4, 1, 2, 3, 2],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Warning Alerts',
        data: [8, 6, 9, 7, 5, 8, 6],
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Info Alerts',
        data: [12, 15, 11, 14, 16, 13, 15],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ],
  };

  const departmentAlertsData = {
    labels: ['Quality Control', 'Energy Management', 'Processing', 'Dispatch', 'Sales', 'Production'],
    datasets: [
      {
        label: 'Alert Count',
        data: [15, 12, 8, 6, 4, 10],
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderColor: [
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(168, 85, 247)',
          'rgb(156, 163, 175)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const alertResolutionData = {
    labels: ['Resolved', 'In Progress', 'Pending', 'Escalated'],
    datasets: [
      {
        data: [45, 25, 20, 10],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(245, 158, 11)',
          'rgb(59, 130, 246)',
          'rgb(239, 68, 68)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const aiAccuracyData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'AI Prediction Accuracy (%)',
        data: [94, 96, 92, 95, 97, 93, 95],
        borderColor: 'rgb(168, 85, 247)',
        backgroundColor: 'rgba(168, 85, 247, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target Accuracy (%)',
        data: [90, 90, 90, 90, 90, 90, 90],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">All Alerts & Notifications</h2>
          <p className="text-gray-600 mt-1">AI-powered alerts across all departments and DCS network</p>
        </div>

        <div className="flex items-center space-x-4">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="overview">Overview</option>
            <option value="dcs-alerts">DCS Alerts</option>
            <option value="alert-analytics">Alert Analytics</option>
          </select>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
            >
              <option value="all">All Types</option>
              <option value="critical">Critical</option>
              <option value="warning">Warning</option>
              <option value="info">Information</option>
            </select>
          </div>

          <select
            value={filterDepartment}
            onChange={(e) => setFilterDepartment(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="all">All Departments</option>
            <option value="quality">Quality Control</option>
            <option value="energy">Energy Management</option>
            <option value="processing">Processing</option>
            <option value="dispatch">Dispatch</option>
            <option value="sales">Sales</option>
          </select>
        </div>
      </div>

      {/* DCS Filters */}
      <DCSFilters
        selectedTaluka={selectedTaluka}
        selectedVillage={selectedVillage}
        selectedPerformance={selectedPerformance}
        onTalukaChange={setSelectedTaluka}
        onVillageChange={setSelectedVillage}
        onPerformanceChange={setSelectedPerformance}
        onClearFilters={handleClearFilters}
        showStats={true}
      />

      {/* Alerts Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600 mb-1">
              {allAlerts.filter(a => a.type === 'critical').length}
            </div>
            <p className="text-sm text-gray-600">Critical Alerts</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600 mb-1">
              {allAlerts.filter(a => a.type === 'warning').length}
            </div>
            <p className="text-sm text-gray-600">Warnings</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {allAlerts.filter(a => a.type === 'info').length}
            </div>
            <p className="text-sm text-gray-600">Information</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {allAlerts.filter(a => a.status === 'active').length}
            </div>
            <p className="text-sm text-gray-600">Active Alerts</p>
          </CardContent>
        </Card>
      </div>

      {/* AI Alert Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alert Trend Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Weekly Alert Trends</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={alertTrendData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Department Alert Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              <span>Department Alert Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={departmentAlertsData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alert Resolution & AI Accuracy */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alert Resolution Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Alert Resolution Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={alertResolutionData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* AI Prediction Accuracy */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-purple-600" />
              <span>AI Prediction Accuracy</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={aiAccuracyData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Alerts List */}
      <div className="space-y-4">
        {filteredAlerts.map((alert) => (
          <Card key={alert.id} className="border-l-4 border-l-red-500 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-4 flex-1">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-gray-900 text-lg">{alert.title}</h3>
                      <Badge className={getCategoryColor(alert.category)}>
                        {alert.category.toUpperCase()}
                      </Badge>
                      <Badge className={getStatusColor(alert.status)}>
                        {alert.status.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-gray-600 mb-3">{alert.description}</p>
                    
                    {/* Location and Department */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-700">{alert.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Bell className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-700">{alert.department}</span>
                      </div>
                    </div>

                    {/* AI Analysis */}
                    <div className="bg-blue-50 p-4 rounded-lg mb-4">
                      <div className="flex items-start space-x-2">
                        <Brain className="h-4 w-4 text-blue-600 mt-1 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-blue-900 mb-1">AI Analysis</h4>
                          <p className="text-sm text-blue-800">{alert.aiAnalysis}</p>
                        </div>
                      </div>
                    </div>

                    {/* Recommended Action */}
                    <div className="bg-green-50 p-4 rounded-lg mb-4">
                      <div className="flex items-start space-x-2">
                        <Zap className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-green-900 mb-1">Recommended Action</h4>
                          <p className="text-sm text-green-800">{alert.recommendedAction}</p>
                        </div>
                      </div>
                    </div>

                    {/* Alert Metadata */}
                    <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-200">
                      <span>Impact: {alert.impact}</span>
                      <span>{alert.time}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* DCS Alerts View */}
      {viewMode === 'dcs-alerts' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* DCS Selector */}
          <div className="lg:col-span-1">
            <DCSSelector
              selectedDCS={selectedDCS}
              onDCSSelect={(dcs) => setSelectedDCS(dcs.id)}
              showDetails={true}
              maxHeight="600px"
              filters={{
                taluka: selectedTaluka,
                village: selectedVillage,
                performance: selectedPerformance
              }}
            />
          </div>

          {/* DCS Alerts Details */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-red-600" />
                  <span>DCS Alerts & Notifications</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getDCSAlertsData().slice(0, 10).map((alert) => (
                    <div
                      key={alert.id}
                      className={`p-4 rounded-lg border-l-4 ${
                        alert.type === 'critical' ? 'border-red-500 bg-red-50' :
                        alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                        'border-blue-500 bg-blue-50'
                      } ${selectedDCS === alert.dcsId ? 'ring-2 ring-blue-300' : ''}`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {alert.type === 'critical' ? (
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                          ) : alert.type === 'warning' ? (
                            <AlertTriangle className="h-5 w-5 text-yellow-600" />
                          ) : (
                            <Bell className="h-5 w-5 text-blue-600" />
                          )}
                          <h3 className="font-semibold text-gray-900">{alert.title}</h3>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={
                            alert.type === 'critical' ? 'bg-red-100 text-red-700' :
                            alert.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-blue-100 text-blue-700'
                          }>
                            {alert.type}
                          </Badge>
                          <span className="text-xs text-gray-500">{alert.time}</span>
                        </div>
                      </div>

                      <p className="text-gray-700 mb-2">{alert.description}</p>

                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{alert.location}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Building className="h-3 w-3" />
                          <span>{alert.department}</span>
                        </div>
                      </div>

                      <div className="bg-white p-3 rounded border mb-3">
                        <div className="flex items-start space-x-2">
                          <Brain className="h-4 w-4 text-purple-600 mt-0.5" />
                          <div>
                            <div className="text-sm font-medium text-purple-900 mb-1">AI Analysis</div>
                            <div className="text-sm text-purple-700">{alert.aiAnalysis}</div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-3 rounded border">
                        <div className="text-sm font-medium text-blue-900 mb-1">Recommended Action</div>
                        <div className="text-sm text-blue-700">{alert.recommendedAction}</div>
                        <div className="text-xs text-blue-600 mt-1">Impact: {alert.impact}</div>
                      </div>
                    </div>
                  ))}

                  {getDCSAlertsData().length === 0 && (
                    <div className="text-center py-12">
                      <CheckCircle className="h-16 w-16 text-green-300 mx-auto mb-4" />
                      <p className="text-gray-500">No active alerts for selected DCS</p>
                      <p className="text-gray-400 text-sm mt-1">All systems operating normally</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Alert Analytics */}
      {viewMode === 'alert-analytics' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <span>DCS Alert Analytics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {['critical', 'warning', 'info'].map((alertType) => {
                const alertsCount = getDCSAlertsData().filter(alert => alert.type === alertType).length;
                return (
                  <div key={alertType} className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${
                        alertType === 'critical' ? 'text-red-600' :
                        alertType === 'warning' ? 'text-yellow-600' :
                        'text-blue-600'
                      }`}>
                        {alertsCount}
                      </div>
                      <div className="text-sm text-gray-700 capitalize">{alertType} Alerts</div>
                    </div>
                  </div>
                );
              })}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {getFilteredDCSData().length - getDCSAlertsData().filter(alert => alert.type === 'critical').length}
                  </div>
                  <div className="text-sm text-green-700">Healthy DCS</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Alerts by Department</h3>
                <div className="space-y-2">
                  {['Quality Control', 'Finance', 'Operations', 'Maintenance'].map((dept) => {
                    const deptAlerts = getDCSAlertsData().filter(alert => alert.department === dept);
                    return (
                      <div key={dept} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <span className="text-sm font-medium text-gray-900">{dept}</span>
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-gray-100 text-gray-700">
                            {deptAlerts.length} alerts
                          </Badge>
                          <div className="flex space-x-1">
                            {deptAlerts.filter(a => a.type === 'critical').length > 0 && (
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            )}
                            {deptAlerts.filter(a => a.type === 'warning').length > 0 && (
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            )}
                            {deptAlerts.filter(a => a.type === 'info').length > 0 && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Alert Locations</h3>
                <div className="space-y-2">
                  {getFilteredDCSData().slice(0, 5).map((dcs) => {
                    const dcsAlerts = getDCSAlertsData().filter(alert => alert.dcsId === dcs.id);
                    return (
                      <div key={dcs.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div>
                          <span className="text-sm font-medium text-gray-900">{dcs.name}</span>
                          <div className="text-xs text-gray-600">{dcs.village}, {dcs.taluka}</div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={
                            dcs.performance === 'excellent' ? 'bg-green-100 text-green-700' :
                            dcs.performance === 'good' ? 'bg-blue-100 text-blue-700' :
                            dcs.performance === 'average' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-red-100 text-red-700'
                          }>
                            {dcsAlerts.length} alerts
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AllAlerts;
