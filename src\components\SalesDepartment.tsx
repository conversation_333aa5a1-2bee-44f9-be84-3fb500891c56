
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Truck,
  TrendingUp,
  TrendingDown,
  MapPin,
  Calendar,
  BarChart3,
  Package,
  IndianRupee,
  Brain,
  Lightbulb,
  AlertTriangle,
  Target,
  Clock,
  Building,
  Users,
  Milk,
  CheckCircle
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";
import DCSFilters from "@/components/filters/DCSFilters";
import DCSSelector from "@/components/filters/DCSSelector";
import { dcsData, filterDCSData, getDCSStats } from "@/data/dcsData";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { <PERSON>, <PERSON>, Doughnut, Pie } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const SalesDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedDCS, setSelectedDCS] = useState("all");
  const [selectedTaluka, setSelectedTaluka] = useState("all");
  const [selectedVillage, setSelectedVillage] = useState("all");
  const [selectedPerformance, setSelectedPerformance] = useState("all");
  const [viewMode, setViewMode] = useState("overview"); // overview, dcs-sales, revenue-analysis

  const regions = ["all", "radhanagri", "hamidwada", "chandgad", "shirol", "kini", "wathar", "gaganbawda"];

  // Get filtered DCS data for sales analysis
  const getFilteredDCSData = () => {
    return filterDCSData({
      taluka: selectedTaluka,
      village: selectedVillage,
      performance: selectedPerformance
    });
  };

  // Generate DCS sales metrics
  const getDCSSalesMetrics = () => {
    const filteredDCS = getFilteredDCSData();

    return filteredDCS.map(dcs => {
      // Calculate sales based on milk collection and pricing
      const basePrice = 45; // per liter
      const qualityMultiplier = dcs.qualityScore / 100;
      const dailySales = dcs.dailyMilkCollection * basePrice * qualityMultiplier;
      const monthlySales = dailySales * 30;

      return {
        id: dcs.id,
        name: dcs.name,
        village: dcs.village,
        taluka: dcs.taluka,
        dailyCollection: dcs.dailyMilkCollection,
        dailySales: Math.round(dailySales),
        monthlySales: Math.round(monthlySales),
        monthlyRevenue: dcs.monthlyRevenue,
        farmers: dcs.activeFarmers,
        qualityScore: dcs.qualityScore,
        performance: dcs.performance,
        avgPrice: Math.round(basePrice * qualityMultiplier),
        growth: (Math.random() * 20 - 5).toFixed(1), // -5% to +15% growth
        marketShare: ((dcs.dailyMilkCollection / 30000) * 100).toFixed(1), // % of total collection
        customerSatisfaction: Math.round(85 + Math.random() * 15), // 85-100%
        images: dcs.images,
        aiInsights: dcs.aiInsights,
        // Product breakdown
        products: {
          milk: Math.round(dailySales * 0.6),
          curd: Math.round(dailySales * 0.15),
          ghee: Math.round(dailySales * 0.12),
          butter: Math.round(dailySales * 0.08),
          paneer: Math.round(dailySales * 0.05)
        }
      };
    });
  };

  const handleClearFilters = () => {
    setSelectedDCS('all');
    setSelectedTaluka('all');
    setSelectedVillage('all');
    setSelectedPerformance('all');
  };

  const salesMetrics = [
    {
      period: "Today",
      revenue: 2850000,
      target: 3000000,
      growth: 12.5,
      orders: 145,
      avgOrderValue: 19655
    },
    {
      period: "This Week",
      revenue: 18500000,
      target: 20000000,
      growth: 8.3,
      orders: 892,
      avgOrderValue: 20740
    },
    {
      period: "This Month",
      revenue: 75200000,
      target: 80000000,
      growth: 15.2,
      orders: 3456,
      avgOrderValue: 21759
    }
  ];

  const productSales = [
    {
      product: "Ghee",
      dailySales: 890000,
      weeklySales: 5800000,
      monthlySales: 24000000,
      growth: 18.5,
      units: 1780,
      avgPrice: 500,
      insight: "Festival-driven surge in Radhanagri - reallocate stocks"
    },
    {
      product: "Dahi",
      dailySales: 420000,
      weeklySales: 2800000,
      monthlySales: 12500000,
      growth: 8.7,
      units: 4000,
      avgPrice: 80,
      insight: "Steady growth - maintain current strategy"
    },
    {
      product: "Paneer",
      dailySales: 650000,
      weeklySales: 4200000,
      monthlySales: 18500000,
      growth: 5.2,
      units: 1400,
      avgPrice: 300,
      insight: "Forecasts +8% demand in Kolhapur urban areas"
    },
    {
      product: "Milk",
      dailySales: 320000,
      weeklySales: 2100000,
      monthlySales: 8900000,
      growth: -12.0,
      units: 13000,
      avgPrice: 50,
      insight: "Declining in Chandgad - needs targeted promotions"
    }
  ];

  const regionalData = [
    {
      region: "Radhanagri",
      sales: 8500000,
      target: 9000000,
      growth: 15.0,
      topProduct: "Ghee",
      distributors: 25,
      penetration: 78,
      aiInsight: "High festival demand - increase ghee production capacity",
      delayRatio: 3.2,
      status: "Excellent"
    },
    {
      region: "Hamidwada", 
      sales: 6200000,
      target: 6500000,
      growth: 8.8,
      topProduct: "Milk",
      distributors: 18,
      penetration: 65,
      aiInsight: "Stable performance - maintain current distribution",
      delayRatio: 5.1,
      status: "Good"
    },
    {
      region: "Chandgad",
      sales: 4800000,
      target: 5200000,
      growth: -12.0,
      topProduct: "Paneer",
      distributors: 15,
      penetration: 52,
      aiInsight: "Declining milk sales - urgent distributor check-in needed",
      delayRatio: 6.8,
      status: "Needs Attention"
    },
    {
      region: "Shirol",
      sales: 3200000,
      target: 3800000,
      growth: 6.2,
      topProduct: "Dahi",
      distributors: 12,
      penetration: 45,
      aiInsight: "Under-penetration area - expand retail network",
      delayRatio: 4.5,
      status: "Average"
    },
    {
      region: "Kini",
      sales: 4500000,
      target: 4800000,
      growth: 11.5,
      topProduct: "Ghee",
      distributors: 16,
      penetration: 58,
      aiInsight: "High growth potential - optimize Kini-Wathar route",
      delayRatio: 8.2,
      status: "Good"
    },
    {
      region: "Wathar",
      sales: 3800000,
      target: 4200000,
      growth: 8.9,
      topProduct: "Milk",
      distributors: 14,
      penetration: 62,
      aiInsight: "Route optimization needed - 20% efficiency gain possible",
      delayRatio: 8.2,
      status: "Average"
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatLargeNumber = (num: number) => {
    if (num >= 10000000) return `${(num / 10000000).toFixed(1)}Cr`;
    if (num >= 100000) return `${(num / 100000).toFixed(1)}L`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getFilteredRegionalData = () => {
    return regionalData.filter(region => 
      selectedRegion === 'all' || region.region.toLowerCase() === selectedRegion
    );
  };

  const getStatusColor = (status: string) => {
    switch(status) {
      case 'Excellent': return 'bg-green-100 text-green-800';
      case 'Good': return 'bg-blue-100 text-blue-800';
      case 'Average': return 'bg-yellow-100 text-yellow-800';
      case 'Needs Attention': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const keyMetrics = [
    {
      title: "July Revenue vs Target",
      current: "₹7.2 Cr achieved vs ₹9 Cr goal",
      insight: "20% shortfall – needs strategic push in lagging SKUs",
      icon: <Target className="h-5 w-5 text-red-600" />,
      status: "warning"
    },
    {
      title: "Ghee Sales - Radhanagri",
      current: "15% increase YoY in 1st week of Shravan",
      insight: "Festival-driven surge → Suggest reallocating stocks to meet demand",
      icon: <TrendingUp className="h-5 w-5 text-green-600" />,
      status: "success"
    },
    {
      title: "Milk Sales - Chandgad Region",
      current: "Declined by 12% in 3 months",
      insight: "AI suggests targeted promotions & distributor check-in",
      icon: <TrendingDown className="h-5 w-5 text-red-600" />,
      status: "error"
    },
    {
      title: "Daily Dispatch Delay Ratio",
      current: "8.2% delays on Kini-Wathar Route",
      insight: "Rescheduling via AI = potential 20% efficiency gain",
      icon: <Clock className="h-5 w-5 text-orange-600" />,
      status: "warning"
    }
  ];

  // Chart Data
  const revenueGrowthData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: 'Revenue (₹ Cr)',
        data: [6.2, 6.8, 7.1, 6.9, 7.5, 7.8, 7.2],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target (₹ Cr)',
        data: [6.5, 7.0, 7.2, 7.5, 7.8, 8.0, 8.2],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const productSalesData = {
    labels: ['Milk', 'Ghee', 'Paneer', 'Curd', 'Butter'],
    datasets: [
      {
        label: 'Sales (₹ Lakhs)',
        data: [450, 180, 120, 95, 75],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(34, 197, 94)',
          'rgb(245, 158, 11)',
          'rgb(168, 85, 247)',
          'rgb(239, 68, 68)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const regionalPerformanceData = {
    labels: ['Radhanagri', 'Hamidwada', 'Chandgad', 'Shirol', 'Kini', 'Wathar'],
    datasets: [
      {
        label: 'Revenue (₹ Lakhs)',
        data: [125, 98, 87, 110, 76, 89],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
      {
        label: 'Target (₹ Lakhs)',
        data: [130, 100, 95, 115, 80, 95],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
    ],
  };

  const customerSegmentData = {
    labels: ['Retail Stores', 'Distributors', 'Direct Sales', 'Online', 'Bulk Orders'],
    datasets: [
      {
        data: [35, 28, 20, 12, 5],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(34, 197, 94)',
          'rgb(245, 158, 11)',
          'rgb(168, 85, 247)',
          'rgb(239, 68, 68)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Sales & Distribution Dashboard</h2>
          <p className="text-sm text-gray-600">Revenue, orders, and DCS performance tracking</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="overview">Overview</option>
            <option value="dcs-sales">DCS Sales</option>
            <option value="revenue-analysis">Revenue Analysis</option>
          </select>
          <select
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            {regions.map(region => (
              <option key={region} value={region}>
                {region === 'all' ? 'All Regions' : region.charAt(0).toUpperCase() + region.slice(1)}
              </option>
            ))}
          </select>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* DCS Filters */}
      <DCSFilters
        selectedTaluka={selectedTaluka}
        selectedVillage={selectedVillage}
        selectedPerformance={selectedPerformance}
        onTalukaChange={setSelectedTaluka}
        onVillageChange={setSelectedVillage}
        onPerformanceChange={setSelectedPerformance}
        onClearFilters={handleClearFilters}
        showStats={true}
      />

      {/* Key Performance Metrics */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <span>Key Performance Metrics & AI Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {keyMetrics.map((metric, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border border-purple-100">
                <div className="flex items-start space-x-3">
                  {metric.icon}
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">{metric.title}</h4>
                    <p className="text-sm text-gray-700 mb-2">{metric.current}</p>
                    <div className="flex items-center space-x-2">
                      <Lightbulb className="h-4 w-4 text-yellow-500" />
                      <p className="text-xs text-gray-600">{metric.insight}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sales Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {salesMetrics.map((metric, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="bg-green-100 p-2 rounded-lg">
                  <IndianRupee className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex items-center space-x-1">
                  {metric.growth > 0 ? 
                    <TrendingUp className="h-4 w-4 text-green-600" /> : 
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  }
                  <span className={`text-sm font-medium ${
                    metric.growth > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.growth > 0 ? '+' : ''}{metric.growth}%
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">{metric.period} Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ₹{formatLargeNumber(metric.revenue)}
                  </p>
                </div>

                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Progress vs Target</span>
                    <span>₹{formatLargeNumber(metric.target)}</span>
                  </div>
                  <Progress value={(metric.revenue / metric.target) * 100} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-2 border-t border-gray-200">
                  <div>
                    <p className="text-xs text-gray-600">Orders</p>
                    <p className="text-lg font-semibold text-gray-900">{metric.orders}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600">Avg Order</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(metric.avgOrderValue)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Product-wise Sales with AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-sky-600" />
            <span>Top 3 Performing Products - AI Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productSales.map((product, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-sky-300 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-sky-100 p-2 rounded-lg">
                      <Package className="h-5 w-5 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{product.product}</h3>
                      <p className="text-sm text-gray-600">{product.units} units sold today</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {product.growth > 0 ? 
                      <TrendingUp className="h-4 w-4 text-green-600" /> : 
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    }
                    <span className={`text-sm font-medium ${
                      product.growth > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {product.growth > 0 ? '+' : ''}{product.growth}%
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-4 gap-4 mb-3">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Daily</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(product.dailySales)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Weekly</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(product.weeklySales)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Monthly</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{formatLargeNumber(product.monthlySales)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Avg Price</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ₹{product.avgPrice}
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                  <div className="flex items-center space-x-2">
                    <Brain className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">AI Insight:</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">{product.insight}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Regional Distribution with Enhanced AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-sky-600" />
            <span>Regional Distribution Performance - Sales Heatmap</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {getFilteredRegionalData().map((region, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-green-100 p-2 rounded-lg">
                      <MapPin className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{region.region}</h3>
                      <p className="text-sm text-gray-600">Top Product: {region.topProduct}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(region.status)}>
                      {region.status}
                    </Badge>
                    <div className="flex items-center space-x-1" title={`${region.growth > 0 ? 'Growth' : 'Decline'}: ${Math.abs(region.growth)}%`}>
                      {region.growth > 0 ? 
                        <TrendingUp className="h-4 w-4 text-green-600" /> : 
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      }
                      <span className={`text-sm font-medium ${
                        region.growth > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {region.growth > 0 ? '+' : ''}{region.growth}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Sales vs Target</span>
                      <span className="text-sm font-medium">
                        ₹{formatLargeNumber(region.sales)} / ₹{formatLargeNumber(region.target)}
                      </span>
                    </div>
                    <Progress value={(region.sales / region.target) * 100} className="h-2" />
                  </div>

                  <div className="grid grid-cols-4 gap-3 pt-3 border-t border-gray-200">
                    <div className="text-center">
                      <p className="text-xs text-gray-600">Distributors</p>
                      <p className="text-lg font-semibold text-gray-900">{region.distributors}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600">Penetration</p>
                      <p className="text-lg font-semibold text-gray-900">{region.penetration}%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-600">Delay Ratio</p>
                      <p className={`text-lg font-semibold ${region.delayRatio > 7 ? 'text-red-600' : 'text-green-600'}`}>
                        {region.delayRatio}%
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-purple-600 font-medium">Status</p>
                      <Badge className={getStatusColor(region.status)} variant="secondary">
                        {region.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-400">
                    <div className="flex items-center space-x-2">
                      <Brain className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-800">AI Insight:</span>
                    </div>
                    <p className="text-sm text-purple-700 mt-1">{region.aiInsight}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sales Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Growth Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Revenue Growth Trend</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={revenueGrowthData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Product Sales Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-green-600" />
              <span>Product Sales Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={productSalesData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Regional Performance & Customer Segments */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Regional Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-purple-600" />
              <span>Regional Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={regionalPerformanceData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Customer Segment Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-orange-600" />
              <span>Customer Segment Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Pie data={customerSegmentData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* DCS Sales Analysis */}
      {viewMode === 'dcs-sales' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* DCS Selector */}
          <div className="lg:col-span-1">
            <DCSSelector
              selectedDCS={selectedDCS}
              onDCSSelect={(dcs) => setSelectedDCS(dcs.id)}
              showDetails={true}
              maxHeight="600px"
              filters={{
                taluka: selectedTaluka,
                village: selectedVillage,
                performance: selectedPerformance
              }}
            />
          </div>

          {/* DCS Sales Details */}
          <div className="lg:col-span-2 space-y-4">
            {getDCSSalesMetrics().slice(0, 6).map((dcs) => (
              <Card key={dcs.id} className={`border-2 ${
                selectedDCS === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{dcs.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className="bg-green-100 text-green-700">
                          <MapPin className="h-3 w-3 mr-1" />
                          {dcs.village}, {dcs.taluka}
                        </Badge>
                        <Badge className={
                          dcs.performance === 'excellent' ? 'bg-green-100 text-green-700' :
                          dcs.performance === 'good' ? 'bg-blue-100 text-blue-700' :
                          dcs.performance === 'average' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }>
                          {dcs.performance}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">₹{(dcs.dailySales / 1000).toFixed(0)}K</div>
                      <div className="text-xs text-gray-600">Daily Sales</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Sales Metrics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-blue-50 p-3 rounded-lg text-center">
                      <IndianRupee className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-blue-900">₹{dcs.avgPrice}</div>
                      <div className="text-xs text-blue-600">Avg Price/L</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg text-center">
                      <Milk className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-green-900">{dcs.dailyCollection}L</div>
                      <div className="text-xs text-green-600">Daily Collection</div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg text-center">
                      <TrendingUp className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-purple-900">{dcs.growth}%</div>
                      <div className="text-xs text-purple-600">Growth</div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg text-center">
                      <Target className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-orange-900">{dcs.marketShare}%</div>
                      <div className="text-xs text-orange-600">Market Share</div>
                    </div>
                  </div>

                  {/* Product Sales Breakdown */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-2">Product Sales Breakdown</div>
                    <div className="grid grid-cols-5 gap-2">
                      {Object.entries(dcs.products).map(([product, sales]) => (
                        <div key={product} className="bg-gray-50 p-2 rounded text-center">
                          <div className="text-xs text-gray-600 capitalize">{product}</div>
                          <div className="text-sm font-medium text-gray-900">₹{(sales / 1000).toFixed(0)}K</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Images */}
                  {dcs.images && dcs.images.length > 0 && (
                    <div className="mb-4">
                      <div className="text-sm font-medium text-gray-700 mb-2">Sales & Distribution Images</div>
                      <div className="flex space-x-2 overflow-x-auto">
                        {dcs.images.slice(0, 4).map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Sales Image ${index + 1}`}
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200 flex-shrink-0"
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* AI Insights */}
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div className="flex items-start space-x-2">
                      <Brain className="h-4 w-4 text-purple-600 mt-0.5" />
                      <div>
                        <div className="text-sm font-medium text-purple-900 mb-1">AI Sales Insight</div>
                        <div className="text-sm text-purple-700 mb-2">{dcs.aiInsights.prediction}</div>
                        <div className="text-xs text-purple-600">
                          Recommendation: {dcs.aiInsights.recommendation}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Revenue Analysis */}
      {viewMode === 'revenue-analysis' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <span>DCS Revenue Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {['excellent', 'good', 'average', 'poor'].map((performance) => {
                const dcsInCategory = getDCSSalesMetrics().filter(dcs => dcs.performance === performance);
                const totalRevenue = dcsInCategory.reduce((sum, dcs) => sum + dcs.monthlySales, 0);
                return (
                  <div key={performance} className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">₹{(totalRevenue / 100000).toFixed(1)}L</div>
                      <div className="text-sm text-blue-700 capitalize">{performance} DCS Revenue</div>
                      <div className="text-xs text-gray-600">{dcsInCategory.length} DCS</div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {getDCSSalesMetrics().map((dcs) => (
                <div key={dcs.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-900">{dcs.name}</h3>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">₹{(dcs.monthlySales / 100000).toFixed(1)}L</div>
                      <div className="text-xs text-gray-600">Monthly Revenue</div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Daily Sales:</span>
                      <span className="font-medium">₹{(dcs.dailySales / 1000).toFixed(0)}K</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Avg Price/L:</span>
                      <span className="font-medium">₹{dcs.avgPrice}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Growth Rate:</span>
                      <span className={`font-medium ${parseFloat(dcs.growth) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {dcs.growth}%
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Market Share:</span>
                      <span className="font-medium">{dcs.marketShare}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SalesDepartment;
