import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Download,
  Filter,
  Calendar,
  BarChart3,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  FileSpreadsheet,
  File,
  Building,
  MapPin,
  Users,
  Milk,
  Eye,
  Share
} from "lucide-react";
import DateRangeFilter from "./DateRangeFilter";
import DCSFilters from "@/components/filters/DCSFilters";
import DCSSelector from "@/components/filters/DCSSelector";
import { dcsData, filterDCSData, getDCSStats } from "@/data/dcsData";

const ReportsDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedRegion, setSelectedRegion] = useState("kolhapur");
  const [selectedDCS, setSelectedDCS] = useState("all");
  const [selectedTaluka, setSelectedTaluka] = useState("all");
  const [selectedVillage, setSelectedVillage] = useState("all");
  const [selectedPerformance, setSelectedPerformance] = useState("all");
  const [viewMode, setViewMode] = useState("overview"); // overview, dcs-reports, custom-reports

  // Get filtered DCS data for reports
  const getFilteredDCSData = () => {
    return filterDCSData({
      taluka: selectedTaluka,
      village: selectedVillage,
      performance: selectedPerformance
    });
  };

  // Generate DCS reports data
  const getDCSReportsData = () => {
    const filteredDCS = getFilteredDCSData();

    return filteredDCS.map(dcs => {
      return {
        id: dcs.id,
        name: dcs.name,
        village: dcs.village,
        taluka: dcs.taluka,
        performance: dcs.performance,
        images: dcs.images,
        // Available reports for this DCS
        reports: {
          daily: {
            name: "Daily Collection Report",
            lastGenerated: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: Math.random() > 0.1 ? 'ready' : 'processing',
            size: `${Math.round(50 + Math.random() * 200)}KB`,
            type: 'PDF'
          },
          quality: {
            name: "Quality Analysis Report",
            lastGenerated: new Date(Date.now() - Math.random() * 48 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: Math.random() > 0.05 ? 'ready' : 'processing',
            size: `${Math.round(100 + Math.random() * 300)}KB`,
            type: 'PDF'
          },
          financial: {
            name: "Financial Summary",
            lastGenerated: new Date(Date.now() - Math.random() * 72 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: Math.random() > 0.15 ? 'ready' : 'processing',
            size: `${Math.round(75 + Math.random() * 150)}KB`,
            type: 'Excel'
          },
          farmer: {
            name: "Farmer Payment Report",
            lastGenerated: new Date(Date.now() - Math.random() * 96 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: Math.random() > 0.08 ? 'ready' : 'processing',
            size: `${Math.round(80 + Math.random() * 120)}KB`,
            type: 'PDF'
          },
          performance: {
            name: "Performance Analytics",
            lastGenerated: new Date(Date.now() - Math.random() * 120 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: Math.random() > 0.12 ? 'ready' : 'processing',
            size: `${Math.round(150 + Math.random() * 250)}KB`,
            type: 'PDF'
          }
        },
        // Report statistics
        stats: {
          totalReports: 5,
          readyReports: Math.floor(Math.random() * 3) + 3, // 3-5 ready reports
          processingReports: Math.floor(Math.random() * 2), // 0-1 processing
          lastAccessed: new Date(Date.now() - Math.random() * 168 * 60 * 60 * 1000).toISOString().split('T')[0],
          downloadCount: Math.floor(Math.random() * 50) + 10, // 10-60 downloads
          avgGenerationTime: Math.round(30 + Math.random() * 120) // 30-150 seconds
        }
      };
    });
  };

  const handleClearFilters = () => {
    setSelectedDCS('all');
    setSelectedTaluka('all');
    setSelectedVillage('all');
    setSelectedPerformance('all');
  };

  const departments = [
    { id: "all", name: "All Departments" },
    { id: "quality", name: "Quality Control" },
    { id: "sales", name: "Sales & Distribution" },
    { id: "production", name: "Production" },
    { id: "energy", name: "Energy Management" },
    { id: "hr", name: "Human Resources" }
  ];

  const regions = [
    { id: "kolhapur", name: "Kolhapur HQ" },
    { id: "pune", name: "Pune Region" },
    { id: "mumbai", name: "Mumbai Region" },
    { id: "nashik", name: "Nashik Region" },
    { id: "nagpur", name: "Nagpur Region" }
  ];

  const reportCategories = [
    {
      title: "Quality Reports",
      icon: <CheckCircle className="h-6 w-6 text-green-600" />,
      reports: [
        { name: "Daily Quality Summary", lastGenerated: "2 hours ago", status: "ready" },
        { name: "Product-wise Defect Analysis", lastGenerated: "1 day ago", status: "ready" },
        { name: "Lab Test Results", lastGenerated: "30 minutes ago", status: "ready" },
        { name: "Packaging Line Performance", lastGenerated: "4 hours ago", status: "processing" }
      ]
    },
    {
      title: "Sales & Distribution",
      icon: <TrendingUp className="h-6 w-6 text-blue-600" />,
      reports: [
        { name: "Regional Sales Performance", lastGenerated: "1 hour ago", status: "ready" },
        { name: "Route Optimization Analysis", lastGenerated: "6 hours ago", status: "ready" },
        { name: "Product Demand Forecast", lastGenerated: "12 hours ago", status: "ready" },
        { name: "Distribution Efficiency", lastGenerated: "2 hours ago", status: "ready" }
      ]
    },
    {
      title: "Production Analytics",
      icon: <BarChart3 className="h-6 w-6 text-purple-600" />,
      reports: [
        { name: "Daily Production Summary", lastGenerated: "30 minutes ago", status: "ready" },
        { name: "Equipment Utilization", lastGenerated: "2 hours ago", status: "ready" },
        { name: "Shift Performance Analysis", lastGenerated: "8 hours ago", status: "ready" },
        { name: "Raw Material Consumption", lastGenerated: "4 hours ago", status: "ready" }
      ]
    },
    {
      title: "Energy & Sustainability",
      icon: <AlertTriangle className="h-6 w-6 text-orange-600" />,
      reports: [
        { name: "Solar Plant Performance", lastGenerated: "1 hour ago", status: "ready" },
        { name: "Cold Storage Efficiency", lastGenerated: "3 hours ago", status: "ready" },
        { name: "Energy Cost Analysis", lastGenerated: "1 day ago", status: "ready" },
        { name: "Sustainability Metrics", lastGenerated: "12 hours ago", status: "ready" }
      ]
    }
  ];

  const handleExport = (format: string, reportName: string) => {
    console.log(`Exporting ${reportName} as ${format}`);
    // Simulate export functionality
    alert(`Exporting ${reportName} as ${format.toUpperCase()} file...`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Reports & Analytics Dashboard</h2>
          <p className="text-sm text-gray-600">Generate and manage DCS reports and documentation</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="overview">Overview</option>
            <option value="dcs-reports">DCS Reports</option>
            <option value="custom-reports">Custom Reports</option>
          </select>
        </div>
      </div>

      {/* DCS Filters */}
      <DCSFilters
        selectedTaluka={selectedTaluka}
        selectedVillage={selectedVillage}
        selectedPerformance={selectedPerformance}
        onTalukaChange={setSelectedTaluka}
        onVillageChange={setSelectedVillage}
        onPerformanceChange={setSelectedPerformance}
        onClearFilters={handleClearFilters}
        showStats={true}
      />

      {/* Filters Section */}
      <Card className="border-blue-200 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100">
          <CardTitle className="flex items-center space-x-2 text-blue-800">
            <Filter className="h-5 w-5" />
            <span>Report Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Date Range</label>
              <DateRangeFilter value={dateRange} onChange={setDateRange} />
            </div>

            {/* Department Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Department</label>
              <select 
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>{dept.name}</option>
                ))}
              </select>
            </div>

            {/* Region Filter */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Region</label>
              <select 
                value={selectedRegion}
                onChange={(e) => setSelectedRegion(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {regions.map(region => (
                  <option key={region.id} value={region.id}>{region.name}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Categories */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {reportCategories.map((category, index) => (
          <Card key={index} className="border-gray-200 shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
              <CardTitle className="flex items-center space-x-3">
                {category.icon}
                <span className="text-gray-800">{category.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {category.reports.map((report, reportIndex) => (
                  <div key={reportIndex} className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{report.name}</h4>
                      <p className="text-sm text-gray-600">Last generated: {report.lastGenerated}</p>
                      <Badge className={`mt-2 ${getStatusColor(report.status)}`}>
                        {report.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleExport('pdf', report.name)}
                        className="hover:bg-red-50"
                        disabled={report.status !== 'ready'}
                      >
                        <File className="h-4 w-4 text-red-600 mr-1" />
                        PDF
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleExport('csv', report.name)}
                        className="hover:bg-green-50"
                        disabled={report.status !== 'ready'}
                      >
                        <FileSpreadsheet className="h-4 w-4 text-green-600 mr-1" />
                        CSV
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card className="border-orange-200 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100">
          <CardTitle className="text-orange-800">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button 
              className="bg-blue-600 hover:bg-blue-700 text-white h-16"
              onClick={() => console.log('Generate custom report')}
            >
              <FileText className="h-6 w-6 mb-2" />
              <span>Custom Report</span>
            </Button>
            <Button 
              variant="outline" 
              className="border-green-300 hover:bg-green-50 h-16"
              onClick={() => console.log('Schedule report')}
            >
              <Calendar className="h-6 w-6 mb-2 text-green-600" />
              <span>Schedule Report</span>
            </Button>
            <Button 
              variant="outline" 
              className="border-purple-300 hover:bg-purple-50 h-16"
              onClick={() => console.log('Export dashboard')}
            >
              <Download className="h-6 w-6 mb-2 text-purple-600" />
              <span>Export Dashboard</span>
            </Button>
            <Button 
              variant="outline" 
              className="border-orange-300 hover:bg-orange-50 h-16"
              onClick={() => console.log('Report settings')}
            >
              <BarChart3 className="h-6 w-6 mb-2 text-orange-600" />
              <span>Settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* DCS Reports Analysis */}
      {viewMode === 'dcs-reports' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* DCS Selector */}
          <div className="lg:col-span-1">
            <DCSSelector
              selectedDCS={selectedDCS}
              onDCSSelect={(dcs) => setSelectedDCS(dcs.id)}
              showDetails={true}
              maxHeight="600px"
              filters={{
                taluka: selectedTaluka,
                village: selectedVillage,
                performance: selectedPerformance
              }}
            />
          </div>

          {/* DCS Reports Details */}
          <div className="lg:col-span-2 space-y-4">
            {getDCSReportsData().slice(0, 6).map((dcs) => (
              <Card key={dcs.id} className={`border-2 ${
                selectedDCS === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{dcs.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className="bg-green-100 text-green-700">
                          <MapPin className="h-3 w-3 mr-1" />
                          {dcs.village}, {dcs.taluka}
                        </Badge>
                        <Badge className={
                          dcs.performance === 'excellent' ? 'bg-green-100 text-green-700' :
                          dcs.performance === 'good' ? 'bg-blue-100 text-blue-700' :
                          dcs.performance === 'average' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }>
                          {dcs.performance}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{dcs.stats.readyReports}</div>
                      <div className="text-xs text-gray-600">Ready Reports</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Report Statistics */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="bg-blue-50 p-3 rounded-lg text-center">
                      <FileText className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-blue-900">{dcs.stats.totalReports}</div>
                      <div className="text-xs text-blue-600">Total Reports</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg text-center">
                      <CheckCircle className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-green-900">{dcs.stats.readyReports}</div>
                      <div className="text-xs text-green-600">Ready</div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg text-center">
                      <Download className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-orange-900">{dcs.stats.downloadCount}</div>
                      <div className="text-xs text-orange-600">Downloads</div>
                    </div>
                  </div>

                  {/* Available Reports */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-2">Available Reports</div>
                    <div className="space-y-2">
                      {Object.entries(dcs.reports).map(([key, report]) => (
                        <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">{report.name}</div>
                            <div className="text-xs text-gray-600">
                              {report.lastGenerated} • {report.size} • {report.type}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getStatusColor(report.status)}>
                              {report.status}
                            </Badge>
                            {report.status === 'ready' && (
                              <div className="flex space-x-1">
                                <Button size="sm" variant="outline" className="h-6 px-2">
                                  <Eye className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="outline" className="h-6 px-2">
                                  <Download className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="outline" className="h-6 px-2">
                                  <Share className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Images */}
                  {dcs.images && dcs.images.length > 0 && (
                    <div className="mb-4">
                      <div className="text-sm font-medium text-gray-700 mb-2">Report Documentation</div>
                      <div className="flex space-x-2 overflow-x-auto">
                        {dcs.images.slice(0, 4).map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Report Documentation ${index + 1}`}
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200 flex-shrink-0"
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Report Access Info */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Last Accessed:</span>
                        <span className="font-medium ml-2">{dcs.stats.lastAccessed}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Avg Generation:</span>
                        <span className="font-medium ml-2">{dcs.stats.avgGenerationTime}s</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Custom Reports */}
      {viewMode === 'custom-reports' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <span>Custom DCS Reports Generator</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {['daily', 'quality', 'financial', 'performance'].map((reportType) => {
                const reportsCount = getDCSReportsData().reduce((count, dcs) => {
                  return count + (dcs.reports[reportType]?.status === 'ready' ? 1 : 0);
                }, 0);
                return (
                  <div key={reportType} className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{reportsCount}</div>
                      <div className="text-sm text-blue-700 capitalize">{reportType} Reports Ready</div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Generate Bulk Reports</h3>
                <div className="space-y-3">
                  <Button className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate All DCS Daily Reports
                  </Button>
                  <Button className="w-full justify-start bg-green-600 hover:bg-green-700 text-white">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Generate Quality Summary Report
                  </Button>
                  <Button className="w-full justify-start bg-purple-600 hover:bg-purple-700 text-white">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Generate Performance Analytics
                  </Button>
                  <Button className="w-full justify-start bg-orange-600 hover:bg-orange-700 text-white">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Generate Financial Summary
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Report Templates</h3>
                <div className="space-y-2">
                  {['Monthly DCS Performance', 'Quarterly Financial Summary', 'Annual Quality Report', 'Farmer Payment Summary'].map((template, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <span className="text-sm font-medium text-gray-900">{template}</span>
                      <Button size="sm" variant="outline">
                        <Download className="h-3 w-3 mr-1" />
                        Use Template
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ReportsDepartment;
