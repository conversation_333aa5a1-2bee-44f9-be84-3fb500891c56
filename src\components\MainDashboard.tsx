import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import DcsMap from "./Dashboard/DcsMap";
import {
  TrendingDown,
  CheckCircle,
  Clock,
  Milk,
  Package,
  AlertTriangle,
  TrendingUp,
  Factory,
  IndianRupee,
  Truck,
  Zap,
  FileText,
  Activity,
  Settings,
  Brain,
  MapPin,
  Building2,
  Users,
  Target,
  BarChart3,
  Heart,
  Droplets,
  Stethoscope
} from "lucide-react";
import DudhSansthaDialog from "./DudhSansthaDialog";
import 'mapbox-gl/dist/mapbox-gl.css';
import DateRangeFilter from "./DateRangeFilter";
import { MapContainer } from "react-leaflet";

interface MainDashboardProps {
  onDepartmentClick?: (departmentId: string) => void;
}

// Import DCS data from DcsMap component
const dcsData = [
  {
    id: 1, name: "<PERSON>ne DCS", village: "Shivane", taluka: "Karveer", lat: 16.7201, lng: 74.2439,
    milkSupply: 420, animalCount: 180, healthIndex: 94.5, doctorAssigned: true, doctorName: "Dr. Patil",
    feedStatus: "Sufficient", farmers: 45, efficiency: 98.5, performance: "excellent", fatContent: 4.2,
    proteinContent: 3.8, establishedYear: 2018, infrastructure: "Modern", coldStorage: true, powerBackup: true
  },
  {
    id: 2, name: "Vadgaon DCS", village: "Vadgaon", taluka: "Hatkanangle", lat: 16.8465, lng: 74.4398,
    milkSupply: 280, animalCount: 128, healthIndex: 78.2, doctorAssigned: false, doctorName: null,
    feedStatus: "Low", farmers: 32, efficiency: 85.3, performance: "needs_attention", fatContent: 3.9,
    proteinContent: 3.5, establishedYear: 2015, infrastructure: "Basic", coldStorage: false, powerBackup: true
  },
  {
    id: 3, name: "Ichalkaranji DCS", village: "Ichalkaranji", taluka: "Shirol", lat: 16.7000, lng: 74.4700,
    milkSupply: 630, animalCount: 245, healthIndex: 96.8, doctorAssigned: true, doctorName: "Dr. Sharma",
    feedStatus: "Sufficient", farmers: 67, efficiency: 99.2, performance: "excellent", fatContent: 4.5,
    proteinContent: 4.1, establishedYear: 2020, infrastructure: "Advanced", coldStorage: true, powerBackup: true
  },
  {
    id: 4, name: "Rankala DCS", village: "Rankala", taluka: "Kolhapur", lat: 16.7050, lng: 74.2400,
    milkSupply: 520, animalCount: 210, healthIndex: 92.1, doctorAssigned: true, doctorName: "Dr. Kulkarni",
    feedStatus: "Sufficient", farmers: 58, efficiency: 97.8, performance: "good", fatContent: 4.1,
    proteinContent: 3.7, establishedYear: 2017, infrastructure: "Modern", coldStorage: true, powerBackup: true
  },
  {
    id: 5, name: "Panhala DCS", village: "Panhala Fort", taluka: "Panhala", lat: 16.8100, lng: 74.1100,
    milkSupply: 340, animalCount: 95, healthIndex: 68.5, doctorAssigned: false, doctorName: null,
    feedStatus: "Critical", farmers: 28, efficiency: 76.2, performance: "critical", fatContent: 3.6,
    proteinContent: 3.2, establishedYear: 2012, infrastructure: "Basic", coldStorage: false, powerBackup: false
  },
  // Additional 20 DCS centers (abbreviated for space)
  { id: 6, name: "Kagal DCS", village: "Kagal", taluka: "Kagal", milkSupply: 380, animalCount: 165, healthIndex: 89.3, efficiency: 94.7, performance: "good", farmers: 42 },
  { id: 7, name: "Ajra DCS", village: "Ajra", taluka: "Ajra", milkSupply: 290, animalCount: 140, healthIndex: 82.1, efficiency: 88.4, performance: "needs_attention", farmers: 35 },
  { id: 8, name: "Radhanagari DCS", village: "Radhanagari", taluka: "Radhanagari", milkSupply: 450, animalCount: 195, healthIndex: 91.7, efficiency: 96.3, performance: "excellent", farmers: 52 },
  { id: 9, name: "Bhudargad DCS", village: "Bhudargad", taluka: "Bhudargad", milkSupply: 320, animalCount: 155, healthIndex: 87.4, efficiency: 92.1, performance: "good", farmers: 38 },
  { id: 10, name: "Chandgad DCS", village: "Chandgad", taluka: "Chandgad", milkSupply: 365, animalCount: 172, healthIndex: 88.9, efficiency: 93.7, performance: "good", farmers: 44 },
  { id: 11, name: "Gadhinglaj DCS", village: "Gadhinglaj", taluka: "Gadhinglaj", milkSupply: 480, animalCount: 220, healthIndex: 95.2, efficiency: 98.1, performance: "excellent", farmers: 62 },
  { id: 12, name: "Shirol DCS", village: "Shirol", taluka: "Shirol", milkSupply: 410, animalCount: 185, healthIndex: 90.6, efficiency: 95.4, performance: "good", farmers: 48 },
  { id: 13, name: "Karveer DCS", village: "Karveer", taluka: "Karveer", milkSupply: 395, animalCount: 178, healthIndex: 89.8, efficiency: 94.2, performance: "good", farmers: 46 },
  { id: 14, name: "Hatkanangle DCS", village: "Hatkanangle", taluka: "Hatkanangle", milkSupply: 355, animalCount: 162, healthIndex: 81.3, efficiency: 87.9, performance: "needs_attention", farmers: 39 },
  { id: 15, name: "Bavda DCS", village: "Bavda", taluka: "Shirol", milkSupply: 275, animalCount: 125, healthIndex: 86.7, efficiency: 91.5, performance: "good", farmers: 33 },
  { id: 16, name: "Kurundwad DCS", village: "Kurundwad", taluka: "Shirol", milkSupply: 425, animalCount: 192, healthIndex: 93.4, efficiency: 96.8, performance: "excellent", farmers: 51 },
  { id: 17, name: "Malkapur DCS", village: "Malkapur", taluka: "Karveer", milkSupply: 310, animalCount: 148, healthIndex: 88.2, efficiency: 92.7, performance: "good", farmers: 37 },
  { id: 18, name: "Nesari DCS", village: "Nesari", taluka: "Ajra", milkSupply: 265, animalCount: 118, healthIndex: 79.6, efficiency: 84.3, performance: "needs_attention", farmers: 29 },
  { id: 19, name: "Shahuwadi DCS", village: "Shahuwadi", taluka: "Shahuwadi", milkSupply: 385, animalCount: 175, healthIndex: 90.1, efficiency: 94.6, performance: "good", farmers: 45 },
  { id: 20, name: "Peth Vadgaon DCS", village: "Peth Vadgaon", taluka: "Hatkanangle", milkSupply: 340, animalCount: 158, healthIndex: 87.9, efficiency: 91.8, performance: "good", farmers: 40 },
  { id: 21, name: "Gaganbawda DCS", village: "Gaganbawda", taluka: "Gaganbawda", milkSupply: 295, animalCount: 135, healthIndex: 83.4, efficiency: 89.2, performance: "needs_attention", farmers: 34 },
  { id: 22, name: "Uchgaon DCS", village: "Uchgaon", taluka: "Kolhapur", milkSupply: 465, animalCount: 205, healthIndex: 94.7, efficiency: 97.3, performance: "excellent", farmers: 56 },
  { id: 23, name: "Jaysingpur DCS", village: "Jaysingpur", taluka: "Shirol", milkSupply: 510, animalCount: 230, healthIndex: 96.1, efficiency: 98.7, performance: "excellent", farmers: 64 },
  { id: 24, name: "Kini DCS", village: "Kini", taluka: "Panhala", milkSupply: 325, animalCount: 152, healthIndex: 89.5, efficiency: 93.1, performance: "good", farmers: 38 },
  { id: 25, name: "Vishalgad DCS", village: "Vishalgad", taluka: "Shahuwadi", milkSupply: 375, animalCount: 168, healthIndex: 91.3, efficiency: 95.2, performance: "excellent", farmers: 43 }
];

const MainDashboard = ({ onDepartmentClick }: MainDashboardProps) => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedLevel, setSelectedLevel] = useState<'district' | 'taluka' | 'village' | 'dcs'>('district');
  const [selectedTaluka, setSelectedTaluka] = useState<string | null>(null);
  const [selectedDcs, setSelectedDcs] = useState<number | null>(null);
  const [selectedVillage, setSelectedVillage] = useState<string | null>(null);

  const [liveData, setLiveData] = useState({
    production: 16.2,
    quality: 96.8,
    energy: 87.2,
    dispatch: 94.5
  });

  // Filter functions
  const getFilteredDcsData = () => {
    let filtered = dcsData;
    if (selectedTaluka) {
      filtered = filtered.filter(dcs => dcs.taluka === selectedTaluka);
    }
    if (selectedDcs) {
      filtered = filtered.filter(dcs => dcs.id === selectedDcs);
    }
    return filtered;
  };

  const getUniqueTalukas = () => {
    return [...new Set(dcsData.map(dcs => dcs.taluka))].sort();
  };

  const filteredData = getFilteredDcsData();

  // Calculate dynamic hierarchical data based on filtered DCS data
  const hierarchicalData = {
    district: {
      name: "Kolhapur District",
      totalProduction: filteredData.reduce((sum, dcs) => sum + dcs.milkSupply, 0),
      totalAnimals: filteredData.reduce((sum, dcs) => sum + dcs.animalCount, 0),
      healthIndex: filteredData.length > 0 ? (filteredData.reduce((sum, dcs) => sum + dcs.healthIndex, 0) / filteredData.length) : 0,
      efficiency: filteredData.length > 0 ? (filteredData.reduce((sum, dcs) => sum + dcs.efficiency, 0) / filteredData.length) : 0,
      wastage: Math.round(filteredData.reduce((sum, dcs) => sum + dcs.milkSupply, 0) * 0.02), // 2% wastage estimate
      resourceUtilization: filteredData.length > 0 ? (filteredData.reduce((sum, dcs) => sum + dcs.efficiency, 0) / filteredData.length) : 0,
      activeDCS: filteredData.length,
      totalTalukas: getUniqueTalukas().length,
      totalVillages: filteredData.length,
      totalFarmers: filteredData.reduce((sum, dcs) => sum + dcs.farmers, 0)
    },
    talukas: [
      {
        id: 1,
        name: "Kolhapur",
        villages: 156,
        dcsCount: 89,
        production: 28500,
        animals: 28500,
        healthIndex: 94.2,
        efficiency: 99.0,
        wastage: 285,
        resourceUtilization: 96.1,
        performance: "excellent",
        villages_data: [
          {
            name: "Rankala",
            dcs: 3,
            production: 1200,
            animals: 1200,
            farmers: 45,
            healthIndex: 95.5,
            efficiency: 99.0,
            wastage: 12,
            resourceUtilization: 97.2,
            performance: "excellent"
          },
          {
            name: "Mahalaxmi",
            dcs: 2,
            production: 890,
            animals: 890,
            farmers: 32,
            healthIndex: 91.2,
            efficiency: 98.0,
            wastage: 18,
            resourceUtilization: 94.8,
            performance: "good"
          },
          {
            name: "Shivaji Nagar",
            dcs: 4,
            production: 1560,
            animals: 1560,
            farmers: 67,
            healthIndex: 93.8,
            efficiency: 98.0,
            wastage: 31,
            resourceUtilization: 95.5,
            performance: "good"
          }
        ]
      },
      {
        id: 2,
        name: "Panhala",
        villages: 98,
        dcsCount: 67,
        production: 22300,
        animals: 22300,
        healthIndex: 89.7,
        efficiency: 98.5,
        wastage: 334,
        resourceUtilization: 92.3,
        performance: "good",
        villages_data: [
          {
            name: "Panhala Fort",
            dcs: 2,
            production: 980,
            animals: 980,
            farmers: 28,
            healthIndex: 87.5,
            efficiency: 97.0,
            wastage: 29,
            resourceUtilization: 89.2,
            performance: "needs_attention"
          }
        ]
      },
      {
        id: 3,
        name: "Shirol",
        villages: 87,
        dcsCount: 72,
        production: 19800,
        animals: 19800,
        healthIndex: 85.2,
        efficiency: 97.5,
        wastage: 495,
        resourceUtilization: 88.7,
        performance: "needs_attention",
        villages_data: [
          {
            name: "Shirol",
            dcs: 4,
            production: 1680,
            animals: 1680,
            farmers: 58,
            healthIndex: 82.1,
            efficiency: 96.0,
            wastage: 67,
            resourceUtilization: 85.3,
            performance: "critical"
          }
        ]
      }
    ]
  };

  // Comprehensive KPI Metrics with Hierarchical Data
  const kpiMetrics = [
    {
      title: "District Production",
      value: `${(hierarchicalData.district.totalProduction / 1000).toFixed(0)}K Litres`,
      target: "245K",
      progress: Math.round((hierarchicalData.district.totalProduction / 245000) * 100),
      trend: "+2.3%",
      status: "good",
      icon: <Milk className="h-6 w-6 text-blue-600" />,
      insight: "Production across all talukas showing consistent growth",
      uplift: 2.3,
      tooltip: "District-wide production from 847 DCS centers across 12 talukas",
      hierarchical: {
        level: "District",
        breakdown: `${hierarchicalData.talukas.length} Talukas, ${hierarchicalData.district.totalVillages} Villages`
      }
    },
    {
      title: "Animal Health Index",
      value: `${hierarchicalData.district.healthIndex}%`,
      target: "95%",
      progress: Math.round((hierarchicalData.district.healthIndex / 95) * 100),
      trend: "+0.5%",
      status: "good",
      icon: <Heart className="h-6 w-6 text-green-600" />,
      insight: "District-wide animal health monitoring across all DCS centers",
      uplift: 0.5,
      tooltip: "Health index aggregated from all villages and DCS centers",
      hierarchical: {
        level: "District",
        breakdown: `${hierarchicalData.district.totalAnimals.toLocaleString()} Animals Monitored`
      }
    },
    {
      title: "Operational Efficiency",
      value: `${hierarchicalData.district.efficiency}%`,
      target: "99.5%",
      progress: Math.round((hierarchicalData.district.efficiency / 99.5) * 100),
      trend: "+0.2%",
      status: "excellent",
      icon: <Target className="h-6 w-6 text-purple-600" />,
      insight: "Optimized operations across all hierarchical levels",
      uplift: 0.2,
      tooltip: "Efficiency measured across District → Taluka → Village → DCS levels",
      hierarchical: {
        level: "Multi-Level",
        breakdown: `${hierarchicalData.district.activeDCS} DCS Centers Active`
      }
    },
    {
      title: "Resource Utilization",
      value: `${hierarchicalData.district.resourceUtilization}%`,
      target: "96%",
      progress: Math.round((hierarchicalData.district.resourceUtilization / 96) * 100),
      trend: "****%",
      status: "good",
      icon: <BarChart3 className="h-6 w-6 text-blue-600" />,
      insight: "Optimal resource allocation across all operational levels",
      uplift: 1.3,
      tooltip: "Resource utilization tracking from village to district level",
      hierarchical: {
        level: "Hierarchical",
        breakdown: "Village → Taluka → District Optimization"
      }
    }
  ];

  const criticalAlerts = [
    {
      id: 1,
      type: "critical",
      category: "major",
      title: "Cold Storage B Temperature Alert",
      description: "Temperature reading: 6.2°C (Target: 4-5°C)",
      time: "2 minutes ago",
      department: "Energy Management",
      location: "Cold Storage Unit B, Building A",
      aiAnalysis: "Compressor efficiency down 15%, potential refrigerant leak detected",
      recommendedAction: "Immediate technician dispatch required, check refrigerant levels",
      impact: "High - Product quality at risk"
    },
    {
      id: 2,
      type: "warning",
      category: "medium", 
      title: "Packaging Line 3 Seal Defects",
      description: "Seal defect rate: 2.1% (Target: <1%)",
      time: "8 minutes ago",
      department: "Quality Control",
      location: "Production Floor B, Line 3",
      aiAnalysis: "Sealing temperature variance detected, pressure inconsistency noted",
      recommendedAction: "Adjust sealing parameters, calibrate pressure settings",
      impact: "Medium - Increased waste, quality concerns"
    },
    {
      id: 3,
      type: "info",
      category: "minor",
      title: "Festival Season Demand Forecast",
      description: "Expected 15% increase in ghee demand for Diwali",
      time: "15 minutes ago",
      department: "Sales",
      location: "Planning Office",
      aiAnalysis: "Historical data shows consistent 12-18% spike during festival periods",
      recommendedAction: "Increase ghee production by 20%, prepare additional packaging",
      impact: "Opportunity - Revenue increase potential"
    }
  ];

  const departmentCards = [
    { 
      id: "quality", 
      name: "Quality Control", 
      icon: Package, 
      alerts: 2, 
      status: "warning", 
      metrics: "96.8%",
      description: "AI quality monitoring"
    },
    { 
      id: "production", 
      name: "Production", 
      icon: Factory, 
      alerts: 0, 
      status: "good", 
      metrics: "16.2K L",
      description: "Real-time production tracking"
    },
    { 
      id: "processing", 
      name: "Processing", 
      icon: Settings, 
      alerts: 1, 
      status: "info", 
      metrics: "Active",
      description: "Predictive maintenance"
    },
    { 
      id: "dispatch", 
      name: "Dispatch & Cold Chain", 
      icon: Truck, 
      alerts: 1, 
      status: "info", 
      metrics: "94.5%",
      description: "Route optimization"
    },
    { 
      id: "energy", 
      name: "Energy Management", 
      icon: Zap, 
      alerts: 1, 
      status: "warning", 
      metrics: "87.2%",
      description: "Solar & grid optimization"
    },
    { 
      id: "reports", 
      name: "Reports", 
      icon: FileText, 
      alerts: 0, 
      status: "good", 
      metrics: "24 Reports",
      description: "Analytics & insights"
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'info': return <Clock className="h-5 w-5 text-blue-600" />;
      default: return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'major': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-white';
      case 'minor': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };



  // Daily insights data
  const todayStats = {
    milkCollection: 2045789, // 20.45 lakh litres
    processing: {
      milkPackets: 1022894, // 10.22 lakh litres  
      wastage: 81832, // 0.82 lakh litres
      byProducts: 122542, // 1.22 lakh litres
      butterMilk: 204579, // 2.04 lakh litres
      curd: 163663, // 1.64 lakh litres
      butter: 40915, // 0.41 lakh litres
      paneer: 40915, // 0.41 lakh litres
      ghee: 20458, // 0.20 lakh litres
      processing: 387931 // 3.88 lakh litres (in process)
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Hierarchical Structure and Real-time Status */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent">
            गोकुळ दूध डेयरी - Hierarchical Operations Center
          </h2>
          <p className="text-gray-600 mt-1">District → Taluka → Village → DCS • Comprehensive KPI Tracking</p>

          {/* Hierarchical Breadcrumb */}
          <div className="flex items-center space-x-2 mt-2 text-sm">
            <Building2 className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-600">Kolhapur District</span>
            {selectedTaluka && (
              <>
                <span className="text-gray-400">→</span>
                <MapPin className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-600">{selectedTaluka} Taluka</span>
              </>
            )}
            {selectedDcs && (
              <>
                <span className="text-gray-400">→</span>
                <Droplets className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-600">
                  {dcsData.find(d => d.id === selectedDcs)?.name}
                </span>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {/* Filters */}
          <div className="flex items-center space-x-2">
            <select
              value={selectedTaluka || ''}
              onChange={(e) => {
                setSelectedTaluka(e.target.value || null);
                setSelectedDcs(null); // Reset DCS selection when taluka changes
              }}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Talukas</option>
              {getUniqueTalukas().map(taluka => (
                <option key={taluka} value={taluka}>{taluka}</option>
              ))}
            </select>

            <select
              value={selectedDcs || ''}
              onChange={(e) => setSelectedDcs(e.target.value ? parseInt(e.target.value) : null)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!selectedTaluka}
            >
              <option value="">All DCS</option>
              {dcsData
                .filter(dcs => !selectedTaluka || dcs.taluka === selectedTaluka)
                .map(dcs => (
                  <option key={dcs.id} value={dcs.id}>{dcs.name}</option>
                ))}
            </select>
          </div>

          <div className="flex items-center space-x-2 bg-green-50 px-3 py-2 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-700">Live Data</span>
          </div>
          <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg">
            <BarChart3 className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">
              {hierarchicalData.district.activeDCS} DCS Active
            </span>
          </div>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* Hierarchical Data Flow Visualization */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-green-50 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <MapPin className="h-6 w-6 text-blue-600" />
            Hierarchical Data Structure: District → Taluka → Village → DCS
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* District Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-blue-200">
              <div className="flex items-center justify-center mb-3">
                <Building2 className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-600">1</div>
              <div className="text-sm font-medium text-gray-700">District</div>
              <div className="text-xs text-gray-500 mt-1">Kolhapur</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Production:</span>
                  <span className="font-medium">{(hierarchicalData.district.totalProduction / 1000).toFixed(0)}K L</span>
                </div>
                <div className="flex justify-between">
                  <span>Animals:</span>
                  <span className="font-medium">{(hierarchicalData.district.totalAnimals / 1000).toFixed(0)}K</span>
                </div>
              </div>
            </div>

            {/* Taluka Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-green-200">
              <div className="flex items-center justify-center mb-3">
                <MapPin className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">{hierarchicalData.district.totalTalukas}</div>
              <div className="text-sm font-medium text-gray-700">Talukas</div>
              <div className="text-xs text-gray-500 mt-1">Regional Hubs</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Avg Efficiency:</span>
                  <span className="font-medium">98.3%</span>
                </div>
                <div className="flex justify-between">
                  <span>Health Index:</span>
                  <span className="font-medium">89.7%</span>
                </div>
              </div>
            </div>

            {/* Village Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-purple-200">
              <div className="flex items-center justify-center mb-3">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-purple-600">4354</div>
              <div className="text-sm font-medium text-gray-700">Villages</div>
              <div className="text-xs text-gray-500 mt-1">Base Operations</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Avg Production:</span>
                  <span className="font-medium">199L</span>
                </div>
                <div className="flex justify-between">
                  <span>Farmers:</span>
                  <span className="font-medium">~45</span>
                </div>
              </div>
            </div>

            {/* DCS Level */}
            <div className="text-center p-4 bg-white rounded-lg border-2 border-orange-200">
              <div className="flex items-center justify-center mb-3">
                <Droplets className="w-8 h-8 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-orange-600">6007</div>
              <div className="text-sm font-medium text-gray-700">DCS Centers</div>
              <div className="text-xs text-gray-500 mt-1">Collection Points</div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Avg Collection:</span>
                  <span className="font-medium">283L</span>
                </div>
                <div className="flex justify-between">
                  <span>Utilization:</span>
                  <span className="font-medium">94.5%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Flow Visualization */}
          <div className="mt-6 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-gray-600">District</span>
              </div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-blue-600 to-green-600"></div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                <span className="text-sm text-gray-600">Taluka</span>
              </div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-green-600 to-purple-600"></div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-600 rounded-full"></div>
                <span className="text-sm text-gray-600">Village</span>
              </div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-purple-600 to-orange-600"></div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-orange-600 rounded-full"></div>
                <span className="text-sm text-gray-600">DCS</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* DCS Map Visualization */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <MapPin className="h-6 w-6 text-blue-600" />
            DCS Map Visualization - Hierarchical Locations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DcsMap
            selectedTaluka={selectedTaluka}
            onDcsSelect={(dcs) => {
              setSelectedDcs(dcs.id);
              setSelectedTaluka(dcs.taluka);
            }}
          />
        </CardContent>
      </Card>

      {/* AI Insights Section */}
      <Card className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Brain className="h-6 w-6 text-purple-600" />
            AI Insights & Analytics
            {(selectedTaluka || selectedDcs) && (
              <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                {selectedDcs ? `DCS Level` : selectedTaluka ? `Taluka Level` : 'District Level'}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Performance Analysis */}
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <BarChart3 className="w-4 h-4 text-blue-600" />
                Performance Analysis
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Excellent Centers:</span>
                  <span className="font-medium text-green-600">
                    {filteredData.filter(d => d.performance === 'excellent').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Good Centers:</span>
                  <span className="font-medium text-blue-600">
                    {filteredData.filter(d => d.performance === 'good').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Need Attention:</span>
                  <span className="font-medium text-yellow-600">
                    {filteredData.filter(d => d.performance === 'needs_attention').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Critical:</span>
                  <span className="font-medium text-red-600">
                    {filteredData.filter(d => d.performance === 'critical').length}
                  </span>
                </div>
              </div>
            </div>

            {/* Production Insights */}
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Milk className="w-4 h-4 text-green-600" />
                Production Insights
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Production:</span>
                  <span className="font-medium text-green-600">
                    {hierarchicalData.district.totalProduction.toLocaleString()}L
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Average per DCS:</span>
                  <span className="font-medium">
                    {Math.round(hierarchicalData.district.totalProduction / hierarchicalData.district.activeDCS)}L
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Top Producer:</span>
                  <span className="font-medium text-blue-600">
                    {filteredData.reduce((max, dcs) => dcs.milkSupply > max.milkSupply ? dcs : max, filteredData[0])?.name.split(' ')[0] || 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Production/Animal:</span>
                  <span className="font-medium">
                    {(hierarchicalData.district.totalProduction / hierarchicalData.district.totalAnimals).toFixed(1)}L
                  </span>
                </div>
              </div>
            </div>

            {/* Health & Quality */}
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Heart className="w-4 h-4 text-red-600" />
                Health & Quality
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Avg Health Index:</span>
                  <span className={`font-medium ${
                    hierarchicalData.district.healthIndex >= 90 ? 'text-green-600' :
                    hierarchicalData.district.healthIndex >= 80 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {hierarchicalData.district.healthIndex.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Doctor Coverage:</span>
                  <span className="font-medium text-blue-600">
                    {Math.round((filteredData.filter(d => d.doctorAssigned).length / filteredData.length) * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Feed Sufficient:</span>
                  <span className="font-medium text-green-600">
                    {Math.round((filteredData.filter(d => d.feedStatus === 'Sufficient').length / filteredData.length) * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Critical Cases:</span>
                  <span className="font-medium text-red-600">
                    {filteredData.filter(d => d.feedStatus === 'Critical' || d.healthIndex < 70).length}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* AI Recommendations */}
          <div className="mt-6 bg-white p-4 rounded-lg border border-purple-200">
            <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
              <Brain className="w-4 h-4 text-purple-600" />
              AI Recommendations
            </h4>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>
                    <strong>High Performers:</strong> {filteredData.filter(d => d.performance === 'excellent').map(d => d.name.split(' ')[0]).join(', ')} showing optimal efficiency
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span>
                    <strong>Attention Needed:</strong> {filteredData.filter(d => d.performance === 'needs_attention').length} centers require immediate support
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Stethoscope className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>
                    <strong>Doctor Assignment:</strong> {filteredData.filter(d => !d.doctorAssigned).length} centers need veterinary support
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <Target className="w-4 h-4 text-purple-600 mt-0.5 flex-shrink-0" />
                  <span>
                    <strong>Efficiency Target:</strong> Potential to increase production by {Math.round((100 - hierarchicalData.district.efficiency) * hierarchicalData.district.totalProduction / 100)}L
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed View Section */}
      {(selectedTaluka || selectedDcs) && (
        <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <FileText className="h-6 w-6 text-green-600" />
              Detailed View - {selectedDcs ? `${filteredData[0]?.name}` : `${selectedTaluka} Taluka`}
              <Badge className="bg-green-100 text-green-800 border-green-200">
                {filteredData.length} {filteredData.length === 1 ? 'Center' : 'Centers'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Summary Statistics */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                  <BarChart3 className="w-4 h-4 text-blue-600" />
                  Summary Statistics
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-lg border">
                    <div className="text-2xl font-bold text-green-600">
                      {hierarchicalData.district.totalProduction.toLocaleString()}L
                    </div>
                    <div className="text-sm text-gray-600">Total Milk Supply</div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border">
                    <div className="text-2xl font-bold text-blue-600">
                      {hierarchicalData.district.totalAnimals.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total Animals</div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border">
                    <div className="text-2xl font-bold text-purple-600">
                      {hierarchicalData.district.totalFarmers.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total Farmers</div>
                  </div>
                  <div className="bg-white p-4 rounded-lg border">
                    <div className="text-2xl font-bold text-orange-600">
                      {hierarchicalData.district.efficiency.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600">Avg Efficiency</div>
                  </div>
                </div>
              </div>

              {/* Individual DCS Details */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                  <Building2 className="w-4 h-4 text-green-600" />
                  DCS Centers Details
                </h4>
                <div className="max-h-80 overflow-y-auto space-y-3">
                  {filteredData.map((dcs) => (
                    <div key={dcs.id} className="bg-white p-4 rounded-lg border hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-start mb-2">
                        <h5 className="font-medium text-gray-800">{dcs.name}</h5>
                        <Badge className={`${
                          dcs.performance === 'excellent' ? 'bg-green-100 text-green-800 border-green-200' :
                          dcs.performance === 'good' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          dcs.performance === 'needs_attention' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                          'bg-red-100 text-red-800 border-red-200'
                        } text-xs border`}>
                          {dcs.performance.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Milk Supply:</span>
                          <span className="font-medium text-green-600">{dcs.milkSupply}L</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Animals:</span>
                          <span className="font-medium">{dcs.animalCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Farmers:</span>
                          <span className="font-medium">{dcs.farmers}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Health Index:</span>
                          <span className={`font-medium ${
                            dcs.healthIndex >= 90 ? 'text-green-600' :
                            dcs.healthIndex >= 80 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {dcs.healthIndex}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Efficiency:</span>
                          <span className="font-medium">{dcs.efficiency}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Doctor:</span>
                          <span className={`font-medium ${dcs.doctorAssigned ? 'text-green-600' : 'text-red-600'}`}>
                            {dcs.doctorAssigned ? '✓ Assigned' : '✗ Needed'}
                          </span>
                        </div>
                      </div>
                      {selectedDcs === dcs.id && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                            <div>Infrastructure: {dcs.infrastructure}</div>
                            <div>Established: {dcs.establishedYear}</div>
                            <div>Cold Storage: {dcs.coldStorage ? '✓' : '✗'}</div>
                            <div>Power Backup: {dcs.powerBackup ? '✓' : '✗'}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Simplified KPI Metrics */}

      {/* Simplified KPI Metrics */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Key Performance Indicators</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {kpiMetrics.map((metric, index) => (
              <Card 
                key={index} 
                className="cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 shadow-lg group bg-white"
                title={metric.tooltip}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-50 to-blue-100">
                      {metric.icon}
                    </div>
                    <div className="flex items-center space-x-1">
                      {metric.trend.startsWith('+') ? 
                        <TrendingUp className="h-4 w-4 text-green-600" /> : 
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      }
                      <span className={`text-sm font-bold ${
                        metric.trend.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {metric.trend}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-end">
                      <div>
                        <p className="text-sm text-gray-600 font-medium">{metric.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                      </div>
                      <Badge className={`${getStatusColor(metric.status)} border`}>
                        {metric.status.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Progress to Target</span>
                        <span>{metric.target}</span>
                      </div>
                      <div className="relative">
                        <Progress value={metric.progress} className="h-3" />
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent to-blue-200 opacity-50 rounded-full animate-pulse"></div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg border border-blue-100">
                      <div className="flex items-start space-x-2">
                        <Activity className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-xs text-gray-700">{metric.insight}</p>
                          <div className="mt-2 flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <span className="text-xs font-medium text-gray-600">
                                {Math.abs(metric.uplift)}% {metric.uplift > 0 ? 'improvement' : 'decline'}
                              </span>
                              <div className={`w-2 h-2 rounded-full ${
                                metric.uplift > 0 ? 'bg-green-500' : 'bg-red-500'
                              }`}></div>
                            </div>
                            {(metric as any).hierarchical && (
                              <div className="text-xs text-blue-600 font-medium">
                                {(metric as any).hierarchical.level}
                              </div>
                            )}
                          </div>
                          {(metric as any).hierarchical && (
                            <div className="mt-1 text-xs text-gray-500">
                              {(metric as any).hierarchical.breakdown}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* DCS Locations Map */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <MapPin className="h-5 w-5 text-green-600" />
            DCS Locations & Performance
          </CardTitle>
          <p className="text-sm text-gray-600">Click on any pin to view detailed DCS information</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Excellent (95%+)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>Good (90-94%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>Needs Attention (&lt;90%)</span>
              </div>
            </div>
            <div 
            //@ts-ignore
              ref={MapContainer} 
              className="w-full h-[400px] rounded-lg border"
              style={{ minHeight: '400px' }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onDepartmentClick?.('quality')}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-semibold">Quality Control</p>
                <p className="text-sm text-gray-600">2 Active Alerts</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onDepartmentClick?.('finance')}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <IndianRupee className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="font-semibold">Finance</p>
                <p className="text-sm text-gray-600">₹2.3L Daily Revenue</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onDepartmentClick?.('dispatch')}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Truck className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="font-semibold">Dispatch</p>
                <p className="text-sm text-gray-600">12 Vehicles Active</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onDepartmentClick?.('production')}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Factory className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="font-semibold">Production</p>
                <p className="text-sm text-gray-600">24/7 Monitoring</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* DCS Dialog - TODO: Fix dialog integration */}
    </div>
  );
};

export default MainDashboard;
