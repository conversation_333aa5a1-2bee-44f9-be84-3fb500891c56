import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Building, 
  MapPin, 
  Filter,
  X,
  ChevronDown
} from "lucide-react";
import { getTalukas, getVillages, getDCSStats } from "@/data/dcsData";

interface DCSFiltersProps {
  selectedDCS?: string;
  selectedTaluka?: string;
  selectedVillage?: string;
  selectedPerformance?: string;
  onDCSChange?: (dcs: string) => void;
  onTalukaChange?: (taluka: string) => void;
  onVillageChange?: (village: string) => void;
  onPerformanceChange?: (performance: string) => void;
  onClearFilters?: () => void;
  showStats?: boolean;
}

const DCSFilters = ({
  selectedDCS = 'all',
  selectedTaluka = 'all',
  selectedVillage = 'all',
  selectedPerformance = 'all',
  onDCSChange,
  onTalukaChange,
  onVillageChange,
  onPerformanceChange,
  onClearFilters,
  showStats = true
}: DCSFiltersProps) => {
  const [talukas, setTalukas] = useState<string[]>([]);
  const [villages, setVillages] = useState<string[]>([]);
  const [stats, setStats] = useState<any>(null);

  const performanceOptions = [
    { value: 'all', label: 'All Performance', color: 'bg-gray-100 text-gray-700' },
    { value: 'excellent', label: 'Excellent', color: 'bg-green-100 text-green-700' },
    { value: 'good', label: 'Good', color: 'bg-blue-100 text-blue-700' },
    { value: 'average', label: 'Average', color: 'bg-yellow-100 text-yellow-700' },
    { value: 'poor', label: 'Poor', color: 'bg-red-100 text-red-700' }
  ];

  useEffect(() => {
    setTalukas(getTalukas());
    setStats(getDCSStats());
  }, []);

  useEffect(() => {
    setVillages(getVillages(selectedTaluka));
  }, [selectedTaluka]);

  const hasActiveFilters = selectedDCS !== 'all' || selectedTaluka !== 'all' || 
                          selectedVillage !== 'all' || selectedPerformance !== 'all';

  const getFilterLabel = (value: string, type: 'taluka' | 'village' | 'performance') => {
    if (value === 'all') {
      return type === 'taluka' ? 'All Talukas' : 
             type === 'village' ? 'All Villages' : 'All Performance';
    }
    return value.charAt(0).toUpperCase() + value.slice(1);
  };

  const getPerformanceColor = (performance: string) => {
    const option = performanceOptions.find(opt => opt.value === performance);
    return option?.color || 'bg-gray-100 text-gray-700';
  };

  return (
    <div className="space-y-4">
      {/* Filter Controls */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">Filters:</span>
            </div>

            {/* Taluka Filter */}
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <select
                value={selectedTaluka}
                onChange={(e) => onTalukaChange?.(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-blue-500"
              >
                {talukas.map(taluka => (
                  <option key={taluka} value={taluka}>
                    {getFilterLabel(taluka, 'taluka')}
                  </option>
                ))}
              </select>
            </div>

            {/* Village Filter */}
            <div className="flex items-center space-x-2">
              <Building className="h-4 w-4 text-gray-500" />
              <select
                value={selectedVillage}
                onChange={(e) => onVillageChange?.(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-blue-500"
                disabled={selectedTaluka === 'all'}
              >
                {villages.map(village => (
                  <option key={village} value={village}>
                    {getFilterLabel(village, 'village')}
                  </option>
                ))}
              </select>
            </div>

            {/* Performance Filter */}
            <div className="flex items-center space-x-2">
              <select
                value={selectedPerformance}
                onChange={(e) => onPerformanceChange?.(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-blue-500"
              >
                {performanceOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className="flex items-center space-x-1 text-red-600 border-red-200 hover:bg-red-50"
              >
                <X className="h-3 w-3" />
                <span>Clear</span>
              </Button>
            )}
          </div>

          {/* Active Filter Tags */}
          {hasActiveFilters && (
            <div className="flex flex-wrap items-center gap-2 mt-3 pt-3 border-t border-blue-200">
              <span className="text-xs text-blue-600 font-medium">Active Filters:</span>
              {selectedTaluka !== 'all' && (
                <Badge className="bg-blue-100 text-blue-700 hover:bg-blue-200">
                  Taluka: {selectedTaluka}
                </Badge>
              )}
              {selectedVillage !== 'all' && (
                <Badge className="bg-green-100 text-green-700 hover:bg-green-200">
                  Village: {selectedVillage}
                </Badge>
              )}
              {selectedPerformance !== 'all' && (
                <Badge className={getPerformanceColor(selectedPerformance)}>
                  {getFilterLabel(selectedPerformance, 'performance')}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      {showStats && stats && (
        <Card className="border-gray-200">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalDCS}</div>
                <div className="text-xs text-gray-600">Total DCS</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.totalActiveFarmers.toLocaleString()}</div>
                <div className="text-xs text-gray-600">Active Farmers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{(stats.totalDailyCollection / 1000).toFixed(1)}K</div>
                <div className="text-xs text-gray-600">Daily Collection (L)</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">₹{(stats.totalMonthlyRevenue / 10000000).toFixed(1)}Cr</div>
                <div className="text-xs text-gray-600">Monthly Revenue</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600">{stats.avgQualityScore}%</div>
                <div className="text-xs text-gray-600">Avg Quality</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600">{stats.performanceCount.excellent}</div>
                <div className="text-xs text-gray-600">Excellent DCS</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DCSFilters;
