export interface DCSData {
  id: string;
  name: string;
  village: string;
  taluka: string;
  district: string;
  establishedYear: number;
  totalFarmers: number;
  activeFarmers: number;
  dailyMilkCollection: number; // in liters
  avgFatContent: number;
  avgSNFContent: number;
  qualityScore: number;
  monthlyRevenue: number; // in rupees
  pendingPayments: number;
  performance: 'excellent' | 'good' | 'average' | 'poor';
  coordinates: {
    lat: number;
    lng: number;
  };
  contactPerson: string;
  phoneNumber: string;
  lastInspectionDate: string;
  nextInspectionDate: string;
  facilities: string[];
  aiInsights: {
    prediction: string;
    recommendation: string;
    riskLevel: 'low' | 'medium' | 'high';
    confidence: number;
  };
  images: string[];
}

export const dcsData: DCSData[] = [
  {
    id: "DCS001",
    name: "Hatkanangale Milk Producers Cooperative Society",
    village: "Hatkanangale",
    taluka: "Hatkanangale",
    district: "Kolhapur",
    establishedYear: 2015,
    totalFarmers: 185,
    activeFarmers: 178,
    dailyMilkCollection: 1420,
    avgFatContent: 3.4,
    avgSNFContent: 8.6,
    qualityScore: 96.8,
    monthlyRevenue: 1512000,
    pendingPayments: 45000,
    performance: 'excellent',
    coordinates: { lat: 16.7050, lng: 74.2433 },
    contactPerson: "Rajesh Patil",
    phoneNumber: "+91 9876543210",
    lastInspectionDate: "2024-01-15",
    nextInspectionDate: "2024-02-15",
    facilities: ["Cold Storage", "Testing Lab", "Bulk Milk Cooler"],
    aiInsights: {
      prediction: "15% increase in milk collection expected during festival season",
      recommendation: "Increase storage capacity and optimize collection routes",
      riskLevel: 'low',
      confidence: 87
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS002", 
    name: "Kini Village Dairy Cooperative",
    village: "Kini",
    taluka: "Radhanagri",
    district: "Kolhapur",
    establishedYear: 2018,
    totalFarmers: 142,
    activeFarmers: 138,
    dailyMilkCollection: 980,
    avgFatContent: 3.2,
    avgSNFContent: 8.4,
    qualityScore: 94.5,
    monthlyRevenue: 1045000,
    pendingPayments: 28000,
    performance: 'good',
    coordinates: { lat: 16.6234, lng: 74.1876 },
    contactPerson: "Suresh Jadhav",
    phoneNumber: "+91 9876543211",
    lastInspectionDate: "2024-01-12",
    nextInspectionDate: "2024-02-12",
    facilities: ["Bulk Milk Cooler", "Testing Equipment"],
    aiInsights: {
      prediction: "Quality improvement needed in fat content consistency",
      recommendation: "Implement farmer training program for cattle nutrition",
      riskLevel: 'medium',
      confidence: 78
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS003",
    name: "Wathar Milk Collection Center",
    village: "Wathar",
    taluka: "Radhanagri", 
    district: "Kolhapur",
    establishedYear: 2017,
    totalFarmers: 98,
    activeFarmers: 95,
    dailyMilkCollection: 756,
    avgFatContent: 3.6,
    avgSNFContent: 8.8,
    qualityScore: 97.2,
    monthlyRevenue: 890000,
    pendingPayments: 15000,
    performance: 'excellent',
    coordinates: { lat: 16.5987, lng: 74.2145 },
    contactPerson: "Mangesh Kumbhar",
    phoneNumber: "+91 9876543212",
    lastInspectionDate: "2024-01-18",
    nextInspectionDate: "2024-02-18",
    facilities: ["Cold Storage", "Bulk Milk Cooler", "Quality Testing"],
    aiInsights: {
      prediction: "Consistent high-quality milk production maintained",
      recommendation: "Consider expanding farmer base in nearby villages",
      riskLevel: 'low',
      confidence: 92
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS004",
    name: "Shirol Farmers Cooperative Society",
    village: "Shirol",
    taluka: "Shirol",
    district: "Kolhapur", 
    establishedYear: 2016,
    totalFarmers: 167,
    activeFarmers: 159,
    dailyMilkCollection: 1245,
    avgFatContent: 3.3,
    avgSNFContent: 8.5,
    qualityScore: 95.1,
    monthlyRevenue: 1320000,
    pendingPayments: 38000,
    performance: 'good',
    coordinates: { lat: 16.7234, lng: 74.5432 },
    contactPerson: "Prakash Shinde",
    phoneNumber: "+91 9876543213",
    lastInspectionDate: "2024-01-10",
    nextInspectionDate: "2024-02-10",
    facilities: ["Bulk Milk Cooler", "Testing Lab", "Storage Tank"],
    aiInsights: {
      prediction: "Seasonal variation in milk quality during monsoon",
      recommendation: "Implement covered cattle sheds and improved drainage",
      riskLevel: 'medium',
      confidence: 81
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS005",
    name: "Chandgad Milk Producers Union",
    village: "Chandgad",
    taluka: "Chandgad",
    district: "Kolhapur",
    establishedYear: 2019,
    totalFarmers: 203,
    activeFarmers: 195,
    dailyMilkCollection: 1580,
    avgFatContent: 3.5,
    avgSNFContent: 8.7,
    qualityScore: 96.3,
    monthlyRevenue: 1680000,
    pendingPayments: 52000,
    performance: 'excellent',
    coordinates: { lat: 16.7845, lng: 74.3876 },
    contactPerson: "Vinod Pawar",
    phoneNumber: "+91 9876543214",
    lastInspectionDate: "2024-01-20",
    nextInspectionDate: "2024-02-20",
    facilities: ["Cold Storage", "Testing Lab", "Bulk Milk Cooler", "Administrative Office"],
    aiInsights: {
      prediction: "Potential for 20% growth with improved collection infrastructure",
      recommendation: "Install additional bulk milk coolers in remote areas",
      riskLevel: 'low',
      confidence: 89
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS006",
    name: "Gaganbawda Dairy Cooperative",
    village: "Gaganbawda",
    taluka: "Gaganbawda",
    district: "Kolhapur",
    establishedYear: 2020,
    totalFarmers: 156,
    activeFarmers: 148,
    dailyMilkCollection: 1120,
    avgFatContent: 3.1,
    avgSNFContent: 8.3,
    qualityScore: 93.8,
    monthlyRevenue: 1190000,
    pendingPayments: 35000,
    performance: 'good',
    coordinates: { lat: 16.5456, lng: 73.8234 },
    contactPerson: "Anil Desai",
    phoneNumber: "+91 9876543215",
    lastInspectionDate: "2024-01-08",
    nextInspectionDate: "2024-02-08",
    facilities: ["Bulk Milk Cooler", "Testing Equipment", "Storage Tank"],
    aiInsights: {
      prediction: "Gradual improvement in milk quality over past 6 months",
      recommendation: "Continue current quality improvement initiatives",
      riskLevel: 'low',
      confidence: 85
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS007",
    name: "Hamidwada Village Cooperative",
    village: "Hamidwada",
    taluka: "Shirol",
    district: "Kolhapur",
    establishedYear: 2017,
    totalFarmers: 89,
    activeFarmers: 84,
    dailyMilkCollection: 645,
    avgFatContent: 3.7,
    avgSNFContent: 8.9,
    qualityScore: 98.1,
    monthlyRevenue: 756000,
    pendingPayments: 12000,
    performance: 'excellent',
    coordinates: { lat: 16.6789, lng: 74.4567 },
    contactPerson: "Shaikh Ibrahim",
    phoneNumber: "+91 9876543216",
    lastInspectionDate: "2024-01-22",
    nextInspectionDate: "2024-02-22",
    facilities: ["Cold Storage", "Quality Testing", "Bulk Milk Cooler"],
    aiInsights: {
      prediction: "Highest quality milk in the region, potential for premium pricing",
      recommendation: "Market as premium quality milk to increase revenue",
      riskLevel: 'low',
      confidence: 94
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS008",
    name: "Peth Vadgaon Milk Society",
    village: "Peth Vadgaon",
    taluka: "Hatkanangale",
    district: "Kolhapur",
    establishedYear: 2016,
    totalFarmers: 134,
    activeFarmers: 127,
    dailyMilkCollection: 890,
    avgFatContent: 3.3,
    avgSNFContent: 8.4,
    qualityScore: 94.7,
    monthlyRevenue: 945000,
    pendingPayments: 22000,
    performance: 'good',
    coordinates: { lat: 16.7123, lng: 74.2789 },
    contactPerson: "Ramesh Kulkarni",
    phoneNumber: "+91 9876543217",
    lastInspectionDate: "2024-01-14",
    nextInspectionDate: "2024-02-14",
    facilities: ["Bulk Milk Cooler", "Testing Lab"],
    aiInsights: {
      prediction: "Stable performance with potential for moderate growth",
      recommendation: "Focus on increasing farmer participation",
      riskLevel: 'low',
      confidence: 79
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS009",
    name: "Gajra Village Dairy Union",
    village: "Gajra",
    taluka: "Shirol",
    district: "Kolhapur",
    establishedYear: 2018,
    totalFarmers: 112,
    activeFarmers: 106,
    dailyMilkCollection: 798,
    avgFatContent: 3.4,
    avgSNFContent: 8.6,
    qualityScore: 95.3,
    monthlyRevenue: 845000,
    pendingPayments: 18000,
    performance: 'good',
    coordinates: { lat: 16.6456, lng: 74.5123 },
    contactPerson: "Santosh More",
    phoneNumber: "+91 9876543218",
    lastInspectionDate: "2024-01-16",
    nextInspectionDate: "2024-02-16",
    facilities: ["Bulk Milk Cooler", "Storage Tank", "Testing Equipment"],
    aiInsights: {
      prediction: "Consistent quality maintenance with room for volume growth",
      recommendation: "Expand collection network to nearby hamlets",
      riskLevel: 'low',
      confidence: 82
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS010",
    name: "Kurundwad Milk Producers Society",
    village: "Kurundwad",
    taluka: "Shirol",
    district: "Kolhapur",
    establishedYear: 2015,
    totalFarmers: 178,
    activeFarmers: 169,
    dailyMilkCollection: 1345,
    avgFatContent: 3.2,
    avgSNFContent: 8.5,
    qualityScore: 94.9,
    monthlyRevenue: 1425000,
    pendingPayments: 41000,
    performance: 'good',
    coordinates: { lat: 16.6834, lng: 74.5876 },
    contactPerson: "Balasaheb Patil",
    phoneNumber: "+91 9876543219",
    lastInspectionDate: "2024-01-11",
    nextInspectionDate: "2024-02-11",
    facilities: ["Cold Storage", "Bulk Milk Cooler", "Testing Lab", "Administrative Office"],
    aiInsights: {
      prediction: "Strong performance with potential for infrastructure upgrade",
      recommendation: "Invest in advanced cooling systems for better quality",
      riskLevel: 'medium',
      confidence: 86
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS011",
    name: "Bhudargad Cooperative Society",
    village: "Bhudargad",
    taluka: "Bhudargad",
    district: "Kolhapur",
    establishedYear: 2019,
    totalFarmers: 95,
    activeFarmers: 89,
    dailyMilkCollection: 567,
    avgFatContent: 3.0,
    avgSNFContent: 8.2,
    qualityScore: 92.4,
    monthlyRevenue: 602000,
    pendingPayments: 25000,
    performance: 'average',
    coordinates: { lat: 16.9012, lng: 74.1234 },
    contactPerson: "Ganesh Sawant",
    phoneNumber: "+91 9876543220",
    lastInspectionDate: "2024-01-05",
    nextInspectionDate: "2024-02-05",
    facilities: ["Bulk Milk Cooler", "Basic Testing"],
    aiInsights: {
      prediction: "Quality improvement needed to meet standards",
      recommendation: "Implement comprehensive farmer training program",
      riskLevel: 'medium',
      confidence: 73
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS012",
    name: "Ajra Milk Collection Center",
    village: "Ajra",
    taluka: "Ajra",
    district: "Kolhapur",
    establishedYear: 2020,
    totalFarmers: 123,
    activeFarmers: 118,
    dailyMilkCollection: 876,
    avgFatContent: 3.5,
    avgSNFContent: 8.7,
    qualityScore: 96.1,
    monthlyRevenue: 930000,
    pendingPayments: 19000,
    performance: 'excellent',
    coordinates: { lat: 16.1234, lng: 74.2345 },
    contactPerson: "Mahesh Kadam",
    phoneNumber: "+91 9876543221",
    lastInspectionDate: "2024-01-19",
    nextInspectionDate: "2024-02-19",
    facilities: ["Cold Storage", "Testing Lab", "Bulk Milk Cooler"],
    aiInsights: {
      prediction: "Excellent quality standards maintained consistently",
      recommendation: "Consider becoming a model DCS for training others",
      riskLevel: 'low',
      confidence: 91
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS013",
    name: "Kagal Farmers Cooperative",
    village: "Kagal",
    taluka: "Kagal",
    district: "Kolhapur",
    establishedYear: 2017,
    totalFarmers: 189,
    activeFarmers: 182,
    dailyMilkCollection: 1456,
    avgFatContent: 3.3,
    avgSNFContent: 8.4,
    qualityScore: 95.6,
    monthlyRevenue: 1545000,
    pendingPayments: 47000,
    performance: 'good',
    coordinates: { lat: 16.5678, lng: 74.3456 },
    contactPerson: "Vijay Chavan",
    phoneNumber: "+91 9876543222",
    lastInspectionDate: "2024-01-13",
    nextInspectionDate: "2024-02-13",
    facilities: ["Cold Storage", "Bulk Milk Cooler", "Testing Lab", "Vehicle Parking"],
    aiInsights: {
      prediction: "Strong growth potential with current infrastructure",
      recommendation: "Optimize collection routes for better efficiency",
      riskLevel: 'low',
      confidence: 84
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS014",
    name: "Panhala Hill Dairy Society",
    village: "Panhala",
    taluka: "Panhala",
    district: "Kolhapur",
    establishedYear: 2016,
    totalFarmers: 76,
    activeFarmers: 71,
    dailyMilkCollection: 534,
    avgFatContent: 3.8,
    avgSNFContent: 9.1,
    qualityScore: 97.8,
    monthlyRevenue: 625000,
    pendingPayments: 8000,
    performance: 'excellent',
    coordinates: { lat: 16.8123, lng: 74.1098 },
    contactPerson: "Dattatray Bhosale",
    phoneNumber: "+91 9876543223",
    lastInspectionDate: "2024-01-21",
    nextInspectionDate: "2024-02-21",
    facilities: ["Cold Storage", "Premium Testing Lab", "Bulk Milk Cooler"],
    aiInsights: {
      prediction: "Premium quality milk due to hill climate advantages",
      recommendation: "Market as premium hill-station milk for higher prices",
      riskLevel: 'low',
      confidence: 96
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS015",
    name: "Bavda Village Cooperative",
    village: "Bavda",
    taluka: "Radhanagri",
    district: "Kolhapur",
    establishedYear: 2018,
    totalFarmers: 67,
    activeFarmers: 63,
    dailyMilkCollection: 445,
    avgFatContent: 3.1,
    avgSNFContent: 8.3,
    qualityScore: 93.2,
    monthlyRevenue: 472000,
    pendingPayments: 14000,
    performance: 'average',
    coordinates: { lat: 16.4567, lng: 74.0987 },
    contactPerson: "Ravi Mane",
    phoneNumber: "+91 9876543224",
    lastInspectionDate: "2024-01-07",
    nextInspectionDate: "2024-02-07",
    facilities: ["Bulk Milk Cooler", "Basic Testing"],
    aiInsights: {
      prediction: "Small but stable operation with growth potential",
      recommendation: "Focus on increasing farmer participation and quality",
      riskLevel: 'medium',
      confidence: 76
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS016",
    name: "Malkapur Dairy Cooperative",
    village: "Malkapur",
    taluka: "Hatkanangale",
    district: "Kolhapur",
    establishedYear: 2019,
    totalFarmers: 145,
    activeFarmers: 139,
    dailyMilkCollection: 1023,
    avgFatContent: 3.2,
    avgSNFContent: 8.5,
    qualityScore: 94.3,
    monthlyRevenue: 1085000,
    pendingPayments: 32000,
    performance: 'good',
    coordinates: { lat: 16.7456, lng: 74.2987 },
    contactPerson: "Ashok Jadhav",
    phoneNumber: "+91 9876543225",
    lastInspectionDate: "2024-01-09",
    nextInspectionDate: "2024-02-09",
    facilities: ["Bulk Milk Cooler", "Testing Equipment", "Storage Tank"],
    aiInsights: {
      prediction: "Steady growth with consistent quality maintenance",
      recommendation: "Implement digital payment system for farmers",
      riskLevel: 'low',
      confidence: 80
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS017",
    name: "Ichalkaranji Urban Dairy",
    village: "Ichalkaranji",
    taluka: "Hatkanangale",
    district: "Kolhapur",
    establishedYear: 2015,
    totalFarmers: 234,
    activeFarmers: 225,
    dailyMilkCollection: 1789,
    avgFatContent: 3.4,
    avgSNFContent: 8.6,
    qualityScore: 96.7,
    monthlyRevenue: 1895000,
    pendingPayments: 58000,
    performance: 'excellent',
    coordinates: { lat: 16.6890, lng: 74.4567 },
    contactPerson: "Sanjay Patil",
    phoneNumber: "+91 9876543226",
    lastInspectionDate: "2024-01-17",
    nextInspectionDate: "2024-02-17",
    facilities: ["Cold Storage", "Advanced Testing Lab", "Bulk Milk Cooler", "Administrative Office", "Vehicle Maintenance"],
    aiInsights: {
      prediction: "Largest collection center with excellent infrastructure",
      recommendation: "Consider expanding to serve as regional hub",
      riskLevel: 'low',
      confidence: 93
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS018",
    name: "Uchgaon Milk Society",
    village: "Uchgaon",
    taluka: "Kagal",
    district: "Kolhapur",
    establishedYear: 2020,
    totalFarmers: 87,
    activeFarmers: 82,
    dailyMilkCollection: 612,
    avgFatContent: 3.0,
    avgSNFContent: 8.1,
    qualityScore: 91.8,
    monthlyRevenue: 649000,
    pendingPayments: 21000,
    performance: 'average',
    coordinates: { lat: 16.5234, lng: 74.3789 },
    contactPerson: "Mohan Shinde",
    phoneNumber: "+91 9876543227",
    lastInspectionDate: "2024-01-06",
    nextInspectionDate: "2024-02-06",
    facilities: ["Bulk Milk Cooler", "Basic Testing"],
    aiInsights: {
      prediction: "New center showing gradual improvement",
      recommendation: "Invest in quality improvement infrastructure",
      riskLevel: 'medium',
      confidence: 71
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS019",
    name: "Karvir Farmers Union",
    village: "Karvir",
    taluka: "Karvir",
    district: "Kolhapur",
    establishedYear: 2016,
    totalFarmers: 198,
    activeFarmers: 189,
    dailyMilkCollection: 1567,
    avgFatContent: 3.5,
    avgSNFContent: 8.8,
    qualityScore: 97.1,
    monthlyRevenue: 1665000,
    pendingPayments: 49000,
    performance: 'excellent',
    coordinates: { lat: 16.7012, lng: 74.2234 },
    contactPerson: "Pandurang Kulkarni",
    phoneNumber: "+91 9876543228",
    lastInspectionDate: "2024-01-20",
    nextInspectionDate: "2024-02-20",
    facilities: ["Cold Storage", "Advanced Testing Lab", "Bulk Milk Cooler", "Quality Control Office"],
    aiInsights: {
      prediction: "Top performer with excellent quality standards",
      recommendation: "Share best practices with other DCS units",
      riskLevel: 'low',
      confidence: 95
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS020",
    name: "Shahuwadi Cooperative Society",
    village: "Shahuwadi",
    taluka: "Shahuwadi",
    district: "Kolhapur",
    establishedYear: 2017,
    totalFarmers: 156,
    activeFarmers: 148,
    dailyMilkCollection: 1134,
    avgFatContent: 3.3,
    avgSNFContent: 8.4,
    qualityScore: 95.2,
    monthlyRevenue: 1203000,
    pendingPayments: 36000,
    performance: 'good',
    coordinates: { lat: 16.3456, lng: 74.1234 },
    contactPerson: "Raghunath Pawar",
    phoneNumber: "+91 9876543229",
    lastInspectionDate: "2024-01-12",
    nextInspectionDate: "2024-02-12",
    facilities: ["Cold Storage", "Bulk Milk Cooler", "Testing Lab"],
    aiInsights: {
      prediction: "Consistent performance with room for optimization",
      recommendation: "Implement automated milk collection system",
      riskLevel: 'low',
      confidence: 83
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS021",
    name: "Gadhinglaj Border Dairy",
    village: "Gadhinglaj",
    taluka: "Gadhinglaj",
    district: "Kolhapur",
    establishedYear: 2018,
    totalFarmers: 167,
    activeFarmers: 159,
    dailyMilkCollection: 1289,
    avgFatContent: 3.4,
    avgSNFContent: 8.7,
    qualityScore: 96.4,
    monthlyRevenue: 1367000,
    pendingPayments: 42000,
    performance: 'excellent',
    coordinates: { lat: 16.2234, lng: 74.3456 },
    contactPerson: "Sunil Deshmukh",
    phoneNumber: "+91 9876543230",
    lastInspectionDate: "2024-01-18",
    nextInspectionDate: "2024-02-18",
    facilities: ["Cold Storage", "Testing Lab", "Bulk Milk Cooler", "Border Facility"],
    aiInsights: {
      prediction: "Strategic location for interstate milk trade",
      recommendation: "Develop as export hub for neighboring states",
      riskLevel: 'low',
      confidence: 88
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS022",
    name: "Radhanagri Tribal Cooperative",
    village: "Radhanagri",
    taluka: "Radhanagri",
    district: "Kolhapur",
    establishedYear: 2019,
    totalFarmers: 78,
    activeFarmers: 73,
    dailyMilkCollection: 456,
    avgFatContent: 3.6,
    avgSNFContent: 8.8,
    qualityScore: 94.1,
    monthlyRevenue: 485000,
    pendingPayments: 16000,
    performance: 'good',
    coordinates: { lat: 16.4123, lng: 74.0567 },
    contactPerson: "Bharat Gavit",
    phoneNumber: "+91 9876543231",
    lastInspectionDate: "2024-01-10",
    nextInspectionDate: "2024-02-10",
    facilities: ["Bulk Milk Cooler", "Community Center", "Testing Equipment"],
    aiInsights: {
      prediction: "Tribal area showing good adoption of dairy practices",
      recommendation: "Provide additional training and support programs",
      riskLevel: 'medium',
      confidence: 77
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS023",
    name: "Jaysingpur Industrial Dairy",
    village: "Jaysingpur",
    taluka: "Shirol",
    district: "Kolhapur",
    establishedYear: 2015,
    totalFarmers: 212,
    activeFarmers: 203,
    dailyMilkCollection: 1678,
    avgFatContent: 3.3,
    avgSNFContent: 8.5,
    qualityScore: 95.8,
    monthlyRevenue: 1782000,
    pendingPayments: 54000,
    performance: 'excellent',
    coordinates: { lat: 16.7789, lng: 74.5567 },
    contactPerson: "Kiran Bhosale",
    phoneNumber: "+91 9876543232",
    lastInspectionDate: "2024-01-16",
    nextInspectionDate: "2024-02-16",
    facilities: ["Cold Storage", "Advanced Testing Lab", "Bulk Milk Cooler", "Industrial Processing Unit"],
    aiInsights: {
      prediction: "Industrial area with high milk demand and supply",
      recommendation: "Expand processing capabilities for value addition",
      riskLevel: 'low',
      confidence: 90
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS024",
    name: "Kolhapur City Dairy Hub",
    village: "Kolhapur",
    taluka: "Karvir",
    district: "Kolhapur",
    establishedYear: 2014,
    totalFarmers: 289,
    activeFarmers: 278,
    dailyMilkCollection: 2134,
    avgFatContent: 3.5,
    avgSNFContent: 8.7,
    qualityScore: 97.5,
    monthlyRevenue: 2267000,
    pendingPayments: 68000,
    performance: 'excellent',
    coordinates: { lat: 16.7050, lng: 74.2433 },
    contactPerson: "Dr. Prakash Shinde",
    phoneNumber: "+91 9876543233",
    lastInspectionDate: "2024-01-22",
    nextInspectionDate: "2024-02-22",
    facilities: ["Cold Storage", "Research Lab", "Bulk Milk Cooler", "Administrative Office", "Training Center"],
    aiInsights: {
      prediction: "Central hub with highest collection and best infrastructure",
      recommendation: "Serve as model center for technology adoption",
      riskLevel: 'low',
      confidence: 97
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  },
  {
    id: "DCS025",
    name: "Nesari Valley Cooperative",
    village: "Nesari",
    taluka: "Gadhinglaj",
    district: "Kolhapur",
    establishedYear: 2020,
    totalFarmers: 92,
    activeFarmers: 87,
    dailyMilkCollection: 634,
    avgFatContent: 3.7,
    avgSNFContent: 9.0,
    qualityScore: 96.9,
    monthlyRevenue: 673000,
    pendingPayments: 17000,
    performance: 'excellent',
    coordinates: { lat: 16.1567, lng: 74.2890 },
    contactPerson: "Sachin Kamble",
    phoneNumber: "+91 9876543234",
    lastInspectionDate: "2024-01-21",
    nextInspectionDate: "2024-02-21",
    facilities: ["Cold Storage", "Premium Testing Lab", "Bulk Milk Cooler"],
    aiInsights: {
      prediction: "Newest center showing excellent quality standards",
      recommendation: "Continue current practices and consider expansion",
      riskLevel: 'low',
      confidence: 92
    },
    images: ["/api/placeholder/400/300", "/api/placeholder/400/300", "/api/placeholder/400/300"]
  }
];

// Utility functions for DCS data management
export const getDCSByTaluka = (taluka: string): DCSData[] => {
  if (taluka === 'all') return dcsData;
  return dcsData.filter(dcs => dcs.taluka.toLowerCase() === taluka.toLowerCase());
};

export const getDCSByVillage = (village: string): DCSData[] => {
  if (village === 'all') return dcsData;
  return dcsData.filter(dcs => dcs.village.toLowerCase() === village.toLowerCase());
};

export const getDCSByPerformance = (performance: string): DCSData[] => {
  if (performance === 'all') return dcsData;
  return dcsData.filter(dcs => dcs.performance === performance);
};

export const getDCSById = (id: string): DCSData | undefined => {
  return dcsData.find(dcs => dcs.id === id);
};

export const getTalukas = (): string[] => {
  const talukas = [...new Set(dcsData.map(dcs => dcs.taluka))];
  return ['all', ...talukas.sort()];
};

export const getVillages = (taluka?: string): string[] => {
  let filteredData = dcsData;
  if (taluka && taluka !== 'all') {
    filteredData = dcsData.filter(dcs => dcs.taluka.toLowerCase() === taluka.toLowerCase());
  }
  const villages = [...new Set(filteredData.map(dcs => dcs.village))];
  return ['all', ...villages.sort()];
};

export const getDCSStats = () => {
  const totalFarmers = dcsData.reduce((sum, dcs) => sum + dcs.totalFarmers, 0);
  const totalActiveFarmers = dcsData.reduce((sum, dcs) => sum + dcs.activeFarmers, 0);
  const totalDailyCollection = dcsData.reduce((sum, dcs) => sum + dcs.dailyMilkCollection, 0);
  const totalMonthlyRevenue = dcsData.reduce((sum, dcs) => sum + dcs.monthlyRevenue, 0);
  const avgQualityScore = dcsData.reduce((sum, dcs) => sum + dcs.qualityScore, 0) / dcsData.length;

  const performanceCount = {
    excellent: dcsData.filter(dcs => dcs.performance === 'excellent').length,
    good: dcsData.filter(dcs => dcs.performance === 'good').length,
    average: dcsData.filter(dcs => dcs.performance === 'average').length,
    poor: dcsData.filter(dcs => dcs.performance === 'poor').length
  };

  return {
    totalDCS: dcsData.length,
    totalFarmers,
    totalActiveFarmers,
    totalDailyCollection,
    totalMonthlyRevenue,
    avgQualityScore: Math.round(avgQualityScore * 10) / 10,
    performanceCount
  };
};

export const filterDCSData = (filters: {
  taluka?: string;
  village?: string;
  performance?: string;
  qualityScore?: { min: number; max: number };
  dailyCollection?: { min: number; max: number };
}): DCSData[] => {
  let filtered = dcsData;

  if (filters.taluka && filters.taluka !== 'all') {
    filtered = filtered.filter(dcs => dcs.taluka.toLowerCase() === filters.taluka!.toLowerCase());
  }

  if (filters.village && filters.village !== 'all') {
    filtered = filtered.filter(dcs => dcs.village.toLowerCase() === filters.village!.toLowerCase());
  }

  if (filters.performance && filters.performance !== 'all') {
    filtered = filtered.filter(dcs => dcs.performance === filters.performance);
  }

  if (filters.qualityScore) {
    filtered = filtered.filter(dcs =>
      dcs.qualityScore >= filters.qualityScore!.min &&
      dcs.qualityScore <= filters.qualityScore!.max
    );
  }

  if (filters.dailyCollection) {
    filtered = filtered.filter(dcs =>
      dcs.dailyMilkCollection >= filters.dailyCollection!.min &&
      dcs.dailyMilkCollection <= filters.dailyCollection!.max
    );
  }

  return filtered;
};
