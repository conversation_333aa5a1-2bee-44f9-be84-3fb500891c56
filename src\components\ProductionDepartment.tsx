import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Factory,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Settings,
  BarChart3,
  Brain,
  Lightbulb,
  Building,
  MapPin,
  Users,
  Milk,
  Thermometer,
  Gauge,
  CheckCircle
} from "lucide-react";
import DateRangeFilter from "@/components/DateRangeFilter";
import DCSFilters from "@/components/filters/DCSFilters";
import DCSSelector from "@/components/filters/DCSSelector";
import { dcsData, filterDCSData, getDCSStats } from "@/data/dcsData";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { <PERSON>, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const ProductionDepartment = () => {
  const [dateRange, setDateRange] = useState("today");
  const [selectedLine, setSelectedLine] = useState("all");
  const [selectedDCS, setSelectedDCS] = useState("all");
  const [selectedTaluka, setSelectedTaluka] = useState("all");
  const [selectedVillage, setSelectedVillage] = useState("all");
  const [selectedPerformance, setSelectedPerformance] = useState("all");
  const [viewMode, setViewMode] = useState("overview"); // overview, dcs-production, efficiency-analysis

  // Get filtered DCS data for production analysis
  const getFilteredDCSData = () => {
    return filterDCSData({
      taluka: selectedTaluka,
      village: selectedVillage,
      performance: selectedPerformance
    });
  };

  // Generate DCS production metrics
  const getDCSProductionMetrics = () => {
    const filteredDCS = getFilteredDCSData();

    return filteredDCS.map(dcs => {
      // Calculate production metrics based on milk collection
      const dailyMilkProduction = dcs.dailyMilkCollection;
      const processingCapacity = dailyMilkProduction * 1.2; // 20% buffer capacity
      const utilizationRate = (dailyMilkProduction / processingCapacity) * 100;

      return {
        id: dcs.id,
        name: dcs.name,
        village: dcs.village,
        taluka: dcs.taluka,
        dailyCollection: dcs.dailyMilkCollection,
        processingCapacity: Math.round(processingCapacity),
        utilizationRate: Math.round(utilizationRate),
        qualityScore: dcs.qualityScore,
        farmers: dcs.activeFarmers,
        performance: dcs.performance,
        facilities: dcs.facilities,
        images: dcs.images,
        aiInsights: dcs.aiInsights,
        // Production breakdown by products
        production: {
          milk: Math.round(dailyMilkProduction * 0.7), // 70% sold as milk
          curd: Math.round(dailyMilkProduction * 0.15 * 0.8), // 15% to curd, 80% yield
          ghee: Math.round(dailyMilkProduction * 0.08 * 0.04), // 8% to ghee, 4% yield
          butter: Math.round(dailyMilkProduction * 0.05 * 0.2), // 5% to butter, 20% yield
          paneer: Math.round(dailyMilkProduction * 0.02 * 0.15) // 2% to paneer, 15% yield
        },
        // Equipment status
        equipment: {
          bulkCooler: dcs.facilities.includes('Bulk Milk Cooler') ? 'operational' : 'not_available',
          coldStorage: dcs.facilities.includes('Cold Storage') ? 'operational' : 'not_available',
          testingLab: dcs.facilities.includes('Testing Lab') ? 'operational' : 'basic',
          temperature: (Math.random() * 2 + 3).toFixed(1), // 3-5°C
          efficiency: Math.round(85 + Math.random() * 15), // 85-100%
          lastMaintenance: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          nextMaintenance: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        // Performance metrics
        metrics: {
          energyEfficiency: Math.round(80 + Math.random() * 20), // 80-100%
          wastageRate: (Math.random() * 3).toFixed(1), // 0-3%
          productionGrowth: (Math.random() * 20 - 5).toFixed(1), // -5% to +15%
          downtimeHours: Math.round(Math.random() * 8), // 0-8 hours per month
          maintenanceCost: Math.round(5000 + Math.random() * 15000) // ₹5K-20K per month
        }
      };
    });
  };

  const handleClearFilters = () => {
    setSelectedDCS('all');
    setSelectedTaluka('all');
    setSelectedVillage('all');
    setSelectedPerformance('all');
  };

  const productionLines = [
    {
      id: "line1",
      name: "Milk Processing Line 1",
      product: "Milk",
      status: "running",
      currentOutput: 850,
      targetOutput: 900,
      efficiency: 94.4,
      downtime: 0.5,
      lastMaintenance: "2 days ago",
      nextMaintenance: "5 days"
    },
    {
      id: "line2", 
      name: "Ghee Production Line",
      product: "Ghee",
      status: "running",
      currentOutput: 125,
      targetOutput: 130,
      efficiency: 96.2,
      downtime: 0,
      lastMaintenance: "1 week ago",
      nextMaintenance: "2 days"
    },
    {
      id: "line3",
      name: "Paneer Processing Line",
      product: "Paneer",
      status: "maintenance",
      currentOutput: 0,
      targetOutput: 180,
      efficiency: 0,
      downtime: 2.5,
      lastMaintenance: "now",
      nextMaintenance: "3 hours"
    },
    {
      id: "line4",
      name: "Curd Production Line",
      product: "Curd",
      status: "running",
      currentOutput: 420,
      targetOutput: 450,
      efficiency: 93.3,
      downtime: 0.2,
      lastMaintenance: "3 days ago",
      nextMaintenance: "4 days"
    }
  ];

  const shiftData = [
    {
      shift: "Morning (6 AM - 2 PM)",
      production: 8500,
      target: 9000,
      efficiency: 94.4,
      workers: 45,
      issues: 2
    },
    {
      shift: "Evening (2 PM - 10 PM)",
      production: 7800,
      target: 8500,
      efficiency: 91.8,
      workers: 42,
      issues: 1
    },
    {
      shift: "Night (10 PM - 6 AM)",
      production: 6200,
      target: 7000,
      efficiency: 88.6,
      workers: 38,
      issues: 3
    }
  ];

  const rawMaterialInventory = [
    {
      material: "Raw Milk",
      current: 45000,
      required: 50000,
      unit: "litres",
      supplier: "Local Farmers",
      lastUpdated: "1 hour ago",
      status: "low"
    },
    {
      material: "Packaging Material",
      current: 8500,
      required: 10000,
      unit: "units",
      supplier: "Pack Solutions",
      lastUpdated: "2 hours ago",
      status: "medium"
    },
    {
      material: "Preservatives",
      current: 850,
      required: 500,
      unit: "kg",
      supplier: "Chem Industries",
      lastUpdated: "30 minutes ago",
      status: "good"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'stopped': return 'bg-red-100 text-red-800';
      case 'good': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Clock className="h-4 w-4 text-green-600" />;
      case 'maintenance': return <Settings className="h-4 w-4 text-yellow-600" />;
      case 'stopped': return <Clock className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const aiInsights = [
    {
      title: "Smart Maintenance Prediction",
      description: "Pasteurizer pump on Line 1 shows early wear patterns. Maintenance recommended in next 48 hours to prevent breakdown",
      impact: "High",
      action: "Schedule maintenance"
    },
    {
      title: "Production Optimization",
      description: "Smart analysis suggests 8% efficiency improvement possible by adjusting Line 2 timing during morning shift",
      impact: "Medium", 
      action: "Adjust shift timing"
    },
    {
      title: "Inventory Management",
      description: "Smart forecasting predicts raw milk shortage in 3 days based on collection patterns and production demand",
      impact: "High",
      action: "Increase collection routes"
    }
  ];

  // Chart Data
  const hourlyProductionData = {
    labels: ['6 AM', '8 AM', '10 AM', '12 PM', '2 PM', '4 PM', '6 PM', '8 PM', '10 PM'],
    datasets: [
      {
        label: 'Line 1 - Milk (L)',
        data: [850, 920, 980, 1050, 1100, 1080, 950, 880, 750],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Line 2 - Ghee (kg)',
        data: [12, 15, 18, 22, 25, 23, 20, 16, 12],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Line 3 - Paneer (kg)',
        data: [45, 52, 58, 65, 70, 68, 62, 55, 48],
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: true,
      }
    ],
  };

  const efficiencyComparisonData = {
    labels: ['Line 1', 'Line 2', 'Line 3'],
    datasets: [
      {
        label: 'Current Efficiency (%)',
        data: [94.4, 96.2, 88.5],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
      {
        label: 'Target Efficiency (%)',
        data: [95, 95, 90],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
    ],
  };

  const downtimeAnalysisData = {
    labels: ['Planned Maintenance', 'Equipment Failure', 'Material Shortage', 'Quality Issues', 'Other'],
    datasets: [
      {
        data: [45, 25, 15, 10, 5],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(168, 85, 247, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
          'rgb(168, 85, 247)',
          'rgb(156, 163, 175)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const qualityTrendData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Quality Score (%)',
        data: [96.5, 97.2, 96.8, 97.5, 96.9, 97.1, 97.3],
        borderColor: 'rgb(168, 85, 247)',
        backgroundColor: 'rgba(168, 85, 247, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target Quality (%)',
        data: [95, 95, 95, 95, 95, 95, 95],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Production Dashboard</h2>
          <p className="text-sm text-gray-600">Real-time production monitoring and DCS optimization</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="overview">Overview</option>
            <option value="dcs-production">DCS Production</option>
            <option value="efficiency-analysis">Efficiency Analysis</option>
          </select>
          <select
            value={selectedLine}
            onChange={(e) => setSelectedLine(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="all">All Production Lines</option>
            {productionLines.map(line => (
              <option key={line.id} value={line.id}>{line.name}</option>
            ))}
          </select>
          <DateRangeFilter value={dateRange} onChange={setDateRange} />
        </div>
      </div>

      {/* DCS Filters */}
      <DCSFilters
        selectedTaluka={selectedTaluka}
        selectedVillage={selectedVillage}
        selectedPerformance={selectedPerformance}
        onTalukaChange={setSelectedTaluka}
        onVillageChange={setSelectedVillage}
        onPerformanceChange={setSelectedPerformance}
        onClearFilters={handleClearFilters}
        showStats={true}
      />

      {/* AI Insights Section for Production Department */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <span>Production Smart Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {aiInsights.map((insight, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border border-purple-100">
                <div className="flex items-start space-x-3">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mt-1" />
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">{insight.title}</h4>
                    <p className="text-sm text-gray-700 mb-3">{insight.description}</p>
                    <div className="flex items-center justify-between">
                      <Badge className={insight.impact === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                        {insight.impact} Impact
                      </Badge>
                      <Button size="sm" variant="outline" className="text-xs">
                        {insight.action}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Production Lines Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Factory className="h-5 w-5 text-sky-600" />
            <span>Production Lines Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productionLines
              .filter(line => selectedLine === 'all' || line.id === selectedLine)
              .map((line, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-sky-300 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="bg-sky-100 p-2 rounded-lg">
                      <Factory className="h-6 w-6 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{line.name}</h3>
                      <p className="text-sm text-gray-600">Product: {line.product}</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(line.status)}>
                    {line.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Current Output</p>
                    <p className="text-xl font-bold text-gray-900">{line.currentOutput}</p>
                    <p className="text-xs text-gray-500">L/hour</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Target Output</p>
                    <p className="text-xl font-bold text-gray-900">{line.targetOutput}</p>
                    <p className="text-xs text-gray-500">L/hour</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Efficiency</p>
                    <p className="text-xl font-bold text-gray-900">{line.efficiency}%</p>
                    <div className="flex items-center justify-center mt-1">
                      {line.efficiency > 95 ? 
                        <TrendingUp className="h-3 w-3 text-green-600" /> : 
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      }
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Downtime</p>
                    <p className="text-xl font-bold text-gray-900">{line.downtime}h</p>
                    <p className="text-xs text-gray-500">today</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Progress value={(line.currentOutput / line.targetOutput) * 100} className="w-48 h-2" />
                    <p className="text-xs text-gray-500">
                      Last maintenance: {line.lastMaintenance} | Next: {line.nextMaintenance}
                    </p>
                  </div>
                  <Badge className={line.status === 'running' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {line.status === 'running' ? 'Operational' : 'Under Maintenance'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Shift-wise Production */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-sky-600" />
            <span>Shift-wise Production Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {shiftData.map((shift, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{shift.shift}</h3>
                  {shift.issues > 0 && (
                    <Badge className="bg-red-500 text-white">
                      {shift.issues} issues
                    </Badge>
                  )}
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Production</span>
                      <span className="text-sm font-medium">
                        {shift.production}L / {shift.target}L
                      </span>
                    </div>
                    <Progress value={(shift.production / shift.target) * 100} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-600">Efficiency</p>
                      <p className="text-lg font-semibold text-gray-900">{shift.efficiency}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-600">Workers</p>
                      <p className="text-lg font-semibold text-gray-900">{shift.workers}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Production Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Hourly Production Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Hourly Production Trend</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={hourlyProductionData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Efficiency Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <span>Line Efficiency Comparison</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={efficiencyComparisonData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Downtime Analysis & Quality Trend */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Downtime Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span>Downtime Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={downtimeAnalysisData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Quality Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <span>Weekly Quality Trend</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={qualityTrendData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Raw Material Inventory */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-sky-600" />
            <span>Raw Material Inventory & Smart Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {rawMaterialInventory.map((material, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="bg-sky-100 p-3 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-sky-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{material.material}</h4>
                    <p className="text-sm text-gray-600">Supplier: {material.supplier}</p>
                    <p className="text-xs text-gray-500">Updated: {material.lastUpdated}</p>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-sm text-gray-600">Current Stock</p>
                  <p className="text-xl font-bold text-gray-900">
                    {material.current.toLocaleString()} {material.unit}
                  </p>
                  <p className="text-xs text-gray-500">
                    Required: {material.required.toLocaleString()} {material.unit}
                  </p>
                </div>

                <div className="text-right">
                  <Badge className={getStatusColor(material.status)}>
                    {material.status}
                  </Badge>
                  {material.status === 'low' && (
                    <p className="text-xs text-red-600 mt-1 font-medium">
                      Smart Alert: Reorder Required
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* DCS Production Analysis */}
      {viewMode === 'dcs-production' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* DCS Selector */}
          <div className="lg:col-span-1">
            <DCSSelector
              selectedDCS={selectedDCS}
              onDCSSelect={(dcs) => setSelectedDCS(dcs.id)}
              showDetails={true}
              maxHeight="600px"
              filters={{
                taluka: selectedTaluka,
                village: selectedVillage,
                performance: selectedPerformance
              }}
            />
          </div>

          {/* DCS Production Details */}
          <div className="lg:col-span-2 space-y-4">
            {getDCSProductionMetrics().slice(0, 6).map((dcs) => (
              <Card key={dcs.id} className={`border-2 ${
                selectedDCS === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{dcs.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className="bg-green-100 text-green-700">
                          <MapPin className="h-3 w-3 mr-1" />
                          {dcs.village}, {dcs.taluka}
                        </Badge>
                        <Badge className={
                          dcs.performance === 'excellent' ? 'bg-green-100 text-green-700' :
                          dcs.performance === 'good' ? 'bg-blue-100 text-blue-700' :
                          dcs.performance === 'average' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }>
                          {dcs.performance}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{dcs.utilizationRate}%</div>
                      <div className="text-xs text-gray-600">Utilization</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Production Metrics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-blue-50 p-3 rounded-lg text-center">
                      <Milk className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-blue-900">{dcs.dailyCollection}L</div>
                      <div className="text-xs text-blue-600">Daily Collection</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg text-center">
                      <Factory className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-green-900">{dcs.processingCapacity}L</div>
                      <div className="text-xs text-green-600">Capacity</div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg text-center">
                      <Gauge className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-purple-900">{dcs.equipment.efficiency}%</div>
                      <div className="text-xs text-purple-600">Efficiency</div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg text-center">
                      <Thermometer className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-orange-900">{dcs.equipment.temperature}°C</div>
                      <div className="text-xs text-orange-600">Temperature</div>
                    </div>
                  </div>

                  {/* Production Breakdown */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-2">Daily Production Breakdown</div>
                    <div className="grid grid-cols-5 gap-2">
                      {Object.entries(dcs.production).map(([product, quantity]) => (
                        <div key={product} className="bg-gray-50 p-2 rounded text-center">
                          <div className="text-xs text-gray-600 capitalize">{product}</div>
                          <div className="text-sm font-medium text-gray-900">{quantity}L</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Equipment Status */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-2">Equipment Status</div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className={`p-2 rounded text-center ${
                        dcs.equipment.bulkCooler === 'operational' ? 'bg-green-50' : 'bg-red-50'
                      }`}>
                        <div className="text-xs text-gray-600">Bulk Cooler</div>
                        <div className={`text-sm font-medium ${
                          dcs.equipment.bulkCooler === 'operational' ? 'text-green-700' : 'text-red-700'
                        }`}>
                          {dcs.equipment.bulkCooler === 'operational' ? 'OK' : 'N/A'}
                        </div>
                      </div>
                      <div className={`p-2 rounded text-center ${
                        dcs.equipment.coldStorage === 'operational' ? 'bg-green-50' : 'bg-red-50'
                      }`}>
                        <div className="text-xs text-gray-600">Cold Storage</div>
                        <div className={`text-sm font-medium ${
                          dcs.equipment.coldStorage === 'operational' ? 'text-green-700' : 'text-red-700'
                        }`}>
                          {dcs.equipment.coldStorage === 'operational' ? 'OK' : 'N/A'}
                        </div>
                      </div>
                      <div className={`p-2 rounded text-center ${
                        dcs.equipment.testingLab === 'operational' ? 'bg-green-50' : 'bg-yellow-50'
                      }`}>
                        <div className="text-xs text-gray-600">Testing Lab</div>
                        <div className={`text-sm font-medium ${
                          dcs.equipment.testingLab === 'operational' ? 'text-green-700' : 'text-yellow-700'
                        }`}>
                          {dcs.equipment.testingLab === 'operational' ? 'Advanced' : 'Basic'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Images */}
                  {dcs.images && dcs.images.length > 0 && (
                    <div className="mb-4">
                      <div className="text-sm font-medium text-gray-700 mb-2">Production Facility Images</div>
                      <div className="flex space-x-2 overflow-x-auto">
                        {dcs.images.slice(0, 4).map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Production Facility ${index + 1}`}
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200 flex-shrink-0"
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* AI Insights */}
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div className="flex items-start space-x-2">
                      <Brain className="h-4 w-4 text-purple-600 mt-0.5" />
                      <div>
                        <div className="text-sm font-medium text-purple-900 mb-1">AI Production Insight</div>
                        <div className="text-sm text-purple-700 mb-2">{dcs.aiInsights.prediction}</div>
                        <div className="text-xs text-purple-600">
                          Recommendation: {dcs.aiInsights.recommendation}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Efficiency Analysis */}
      {viewMode === 'efficiency-analysis' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <span>DCS Production Efficiency Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {['excellent', 'good', 'average', 'poor'].map((performance) => {
                const dcsInCategory = getDCSProductionMetrics().filter(dcs => dcs.performance === performance);
                const avgUtilization = dcsInCategory.reduce((sum, dcs) => sum + dcs.utilizationRate, 0) / dcsInCategory.length || 0;
                return (
                  <div key={performance} className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{avgUtilization.toFixed(1)}%</div>
                      <div className="text-sm text-blue-700 capitalize">{performance} DCS Avg</div>
                      <div className="text-xs text-gray-600">{dcsInCategory.length} DCS</div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {getDCSProductionMetrics().map((dcs) => (
                <div key={dcs.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-900">{dcs.name}</h3>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{dcs.utilizationRate}%</div>
                      <div className="text-xs text-gray-600">Utilization</div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Daily Collection:</span>
                      <span className="font-medium">{dcs.dailyCollection}L</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Processing Capacity:</span>
                      <span className="font-medium">{dcs.processingCapacity}L</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Equipment Efficiency:</span>
                      <span className="font-medium">{dcs.equipment.efficiency}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Energy Efficiency:</span>
                      <span className="font-medium">{dcs.metrics.energyEfficiency}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Wastage Rate:</span>
                      <span className="font-medium">{dcs.metrics.wastageRate}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProductionDepartment;
