import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, CircleMarker } from 'react-leaflet';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  MapPin, 
  Milk, 
  IndianRupee, 
  Users, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Dudh Sanstha Data with coordinates
const dudhSansthaData = [
  { 
    name: 'Hatkanangale Sangh', 
    milkCollected: 42000, 
    avgRate: 36.00, 
    totalAmount: 1512000, 
    amountPaid: 1300000, 
    pending: 212000, 
    lastPayment: '12-07-2025',
    performance: 'excellent',
    coordinates: [16.5167, 74.7667], // Hatkanangale coordinates
    taluka: 'Hatkanangale',
    farmers: 1250,
    villages: 45
  },
  { 
    name: 'Gokulwadi Sangh', 
    milkCollected: 31500, 
    avgRate: 35.75, 
    totalAmount: 1126125, 
    amountPaid: 1126125, 
    pending: 0, 
    lastPayment: '14-07-2025',
    performance: 'excellent',
    coordinates: [16.6833, 74.2833], // Gokulwadi coordinates
    taluka: 'Karvir',
    farmers: 980,
    villages: 32
  },
  { 
    name: 'Kagal Sangh', 
    milkCollected: 28750, 
    avgRate: 35.50, 
    totalAmount: 1021625, 
    amountPaid: 900000, 
    pending: 121625, 
    lastPayment: '10-07-2025',
    performance: 'good',
    coordinates: [16.5833, 74.3167], // Kagal coordinates
    taluka: 'Kagal',
    farmers: 875,
    villages: 38
  },
  { 
    name: 'Shirol Sangh', 
    milkCollected: 34000, 
    avgRate: 36.20, 
    totalAmount: 1230800, 
    amountPaid: 1230800, 
    pending: 0, 
    lastPayment: '14-07-2025',
    performance: 'excellent',
    coordinates: [16.7167, 74.5833], // Shirol coordinates
    taluka: 'Shirol',
    farmers: 1100,
    villages: 42
  },
  { 
    name: 'Ajara Sangh', 
    milkCollected: 18500, 
    avgRate: 35.00, 
    totalAmount: 647500, 
    amountPaid: 500000, 
    pending: 147500, 
    lastPayment: '08-07-2025',
    performance: 'needs_attention',
    coordinates: [16.1167, 74.2167], // Ajara coordinates
    taluka: 'Ajra',
    farmers: 650,
    villages: 28
  }
];

interface DudhSansthaMapProps {
  selectedSangh?: string;
  onSanghSelect?: (sangh: any) => void;
}

const DudhSansthaMap = ({ selectedSangh, onSanghSelect }: DudhSansthaMapProps) => {
  const [selectedMarker, setSelectedMarker] = useState<any>(null);

  // Center of Kolhapur district
  const center: [number, number] = [16.7050, 74.2433];

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return '#10b981'; // green
      case 'good': return '#3b82f6'; // blue
      case 'needs_attention': return '#f59e0b'; // amber
      default: return '#6b7280'; // gray
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'good': return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case 'needs_attention': return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const handleMarkerClick = (sangh: any) => {
    setSelectedMarker(sangh);
    onSanghSelect?.(sangh);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            <span>Dudh Sanstha Network Map</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Map */}
            <div className="lg:col-span-2">
              <div className="h-96 rounded-lg overflow-hidden border border-gray-200">
                <MapContainer
                  center={center}
                  zoom={10}
                  style={{ height: '100%', width: '100%' }}
                  className="z-0"
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  
                  {dudhSansthaData.map((sangh, index) => (
                    <CircleMarker
                      key={index}
                      center={sangh.coordinates as [number, number]}
                      radius={Math.max(8, sangh.milkCollected / 5000)} // Size based on milk collection
                      fillColor={getPerformanceColor(sangh.performance)}
                      color="#ffffff"
                      weight={2}
                      opacity={1}
                      fillOpacity={0.8}
                      eventHandlers={{
                        click: () => handleMarkerClick(sangh),
                      }}
                    >
                      <Popup>
                        <div className="p-2 min-w-64">
                          <h3 className="font-semibold text-gray-900 mb-2">{sangh.name}</h3>
                          <div className="space-y-1 text-sm">
                            <div className="flex items-center justify-between">
                              <span className="text-gray-600">Milk Collected:</span>
                              <span className="font-medium">{sangh.milkCollected.toLocaleString()}L</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-gray-600">Avg Rate:</span>
                              <span className="font-medium">₹{sangh.avgRate}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-gray-600">Farmers:</span>
                              <span className="font-medium">{sangh.farmers}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-gray-600">Pending:</span>
                              <span className={`font-medium ${sangh.pending > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                ₹{sangh.pending.toLocaleString()}
                              </span>
                            </div>
                          </div>
                          <div className="mt-2 pt-2 border-t border-gray-200">
                            <Badge className={
                              sangh.performance === 'excellent' ? 'bg-green-100 text-green-700' :
                              sangh.performance === 'good' ? 'bg-blue-100 text-blue-700' :
                              'bg-amber-100 text-amber-700'
                            }>
                              {sangh.performance.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                      </Popup>
                    </CircleMarker>
                  ))}
                </MapContainer>
              </div>
            </div>

            {/* Sangh List */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">Dudh Sanstha List</h3>
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {dudhSansthaData.map((sangh, index) => (
                  <Card
                    key={index}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md border-2 ${
                      selectedMarker?.name === sangh.name 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                    onClick={() => handleMarkerClick(sangh)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 text-sm">{sangh.name}</h4>
                          <p className="text-xs text-gray-600">{sangh.taluka} Taluka</p>
                        </div>
                        {getPerformanceIcon(sangh.performance)}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="flex items-center space-x-1">
                          <Milk className="h-3 w-3 text-blue-500" />
                          <span>{(sangh.milkCollected / 1000).toFixed(0)}K L</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-3 w-3 text-green-500" />
                          <span>{sangh.farmers}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <IndianRupee className="h-3 w-3 text-purple-500" />
                          <span>₹{sangh.avgRate}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <AlertTriangle className={`h-3 w-3 ${sangh.pending > 0 ? 'text-red-500' : 'text-green-500'}`} />
                          <span className={sangh.pending > 0 ? 'text-red-600' : 'text-green-600'}>
                            {sangh.pending > 0 ? `₹${(sangh.pending / 1000).toFixed(0)}K` : 'Paid'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Sangh Details */}
      {selectedMarker && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{selectedMarker.name} - Details</span>
              <Badge className={
                selectedMarker.performance === 'excellent' ? 'bg-green-100 text-green-700' :
                selectedMarker.performance === 'good' ? 'bg-blue-100 text-blue-700' :
                'bg-amber-100 text-amber-700'
              }>
                {selectedMarker.performance.replace('_', ' ')}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  {selectedMarker.milkCollected.toLocaleString()}L
                </div>
                <div className="text-xs text-gray-600">Milk Collected</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-green-600">
                  ₹{selectedMarker.avgRate}
                </div>
                <div className="text-xs text-gray-600">Average Rate</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-purple-600">
                  {selectedMarker.farmers}
                </div>
                <div className="text-xs text-gray-600">Farmers</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className={`text-lg font-bold ${selectedMarker.pending > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  ₹{selectedMarker.pending.toLocaleString()}
                </div>
                <div className="text-xs text-gray-600">Pending Amount</div>
              </div>
            </div>
            
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Total Amount:</span>
                <span className="font-medium ml-2">₹{selectedMarker.totalAmount.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-600">Amount Paid:</span>
                <span className="font-medium ml-2">₹{selectedMarker.amountPaid.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-600">Last Payment:</span>
                <span className="font-medium ml-2">{selectedMarker.lastPayment}</span>
              </div>
              <div>
                <span className="text-gray-600">Villages Covered:</span>
                <span className="font-medium ml-2">{selectedMarker.villages}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DudhSansthaMap;
