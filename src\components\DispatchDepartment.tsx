
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Truck,
  MapPin,
  Clock,
  TrendingUp,
  Snowflake,
  Route,
  Package,
  Brain,
  Thermometer,
  Building,
  Users,
  Milk,
  Navigation,
  Fuel,
  CheckCircle
} from "lucide-react";
import DCSFilters from "@/components/filters/DCSFilters";
import DCSSelector from "@/components/filters/DCSSelector";
import { dcsData, filterDCSData, getDCSStats } from "@/data/dcsData";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const DispatchDepartment = () => {
  const [selectedView, setSelectedView] = useState("dispatch");
  const [selectedDCS, setSelectedDCS] = useState("all");
  const [selectedTaluka, setSelectedTaluka] = useState("all");
  const [selectedVillage, setSelectedVillage] = useState("all");
  const [selectedPerformance, setSelectedPerformance] = useState("all");
  const [viewMode, setViewMode] = useState("overview"); // overview, dcs-dispatch, route-optimization

  // Get filtered DCS data for dispatch analysis
  const getFilteredDCSData = () => {
    return filterDCSData({
      taluka: selectedTaluka,
      village: selectedVillage,
      performance: selectedPerformance
    });
  };

  // Generate DCS dispatch metrics
  const getDCSDispatchMetrics = () => {
    const filteredDCS = getFilteredDCSData();

    return filteredDCS.map(dcs => {
      // Calculate dispatch metrics
      const dailyVolume = dcs.dailyMilkCollection;
      const vehicleCapacity = 1000; // 1000L capacity per vehicle
      const vehiclesNeeded = Math.ceil(dailyVolume / vehicleCapacity);

      return {
        id: dcs.id,
        name: dcs.name,
        village: dcs.village,
        taluka: dcs.taluka,
        dailyCollection: dcs.dailyMilkCollection,
        vehiclesNeeded,
        farmers: dcs.activeFarmers,
        performance: dcs.performance,
        coordinates: dcs.coordinates,
        images: dcs.images,
        aiInsights: dcs.aiInsights,
        // Dispatch schedule
        dispatch: {
          collectionTime: "05:30",
          departureTime: "07:00",
          estimatedArrival: "09:30",
          route: `${dcs.village} → Processing Plant`,
          distance: Math.round(10 + Math.random() * 40), // 10-50 km
          fuelConsumption: Math.round(dailyVolume * 0.05), // 0.05L fuel per L milk
          driverAssigned: `Driver ${Math.floor(Math.random() * 20) + 1}`,
          vehicleNumber: `MH-09-${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}-${Math.floor(Math.random() * 9000) + 1000}`
        },
        // Logistics metrics
        logistics: {
          onTimeDelivery: Math.round(85 + Math.random() * 15), // 85-100%
          fuelEfficiency: (Math.random() * 3 + 12).toFixed(1), // 12-15 km/L
          routeOptimization: Math.round(70 + Math.random() * 30), // 70-100%
          vehicleUtilization: Math.round(dailyVolume / vehicleCapacity * 100), // % of capacity used
          deliveryTime: Math.round(120 + Math.random() * 60), // 120-180 minutes
          costPerLiter: (Math.random() * 2 + 3).toFixed(2) // ₹3-5 per liter
        },
        // Vehicle status
        vehicle: {
          status: Math.random() > 0.1 ? 'operational' : 'maintenance',
          temperature: (Math.random() * 2 + 3).toFixed(1), // 3-5°C
          gpsTracking: 'active',
          lastMaintenance: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          nextMaintenance: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          fuelLevel: Math.round(20 + Math.random() * 80), // 20-100%
          mileage: Math.round(50000 + Math.random() * 100000) // 50K-150K km
        }
      };
    });
  };

  const handleClearFilters = () => {
    setSelectedDCS('all');
    setSelectedTaluka('all');
    setSelectedVillage('all');
    setSelectedPerformance('all');
  };

  const dispatchData = [
    {
      vehicle: "MH-09-AB-1234",
      route: "Kini - Wathar - Peth Vadgaon",
      status: "in-transit",
      driver: "Rajesh Patil",
      products: "Milk: 500L, Curd: 200kg",
      departure: "06:30 AM",
      expectedReturn: "11:30 AM",
      fuelEfficiency: 12.5,
      aiOptimization: "Route optimized - 18% fuel saving"
    },
    {
      vehicle: "MH-09-CD-5678", 
      route: "Shiroli - Hamidwada - Gajra",
      status: "loading",
      driver: "Suresh Jadhav",
      products: "Ghee: 100kg, Butter: 150kg",
      departure: "07:00 AM",
      expectedReturn: "12:00 PM",
      fuelEfficiency: 11.8,
      aiOptimization: "Loading sequence optimized"
    },
    {
      vehicle: "MH-09-EF-9012",
      route: "Gaganbawada - Radhanagari",
      status: "completed",
      driver: "Mahesh Koli",
      products: "Mixed products: 800kg",
      departure: "05:00 AM",
      expectedReturn: "10:00 AM",
      actualReturn: "09:45 AM",
      fuelEfficiency: 13.2,
      aiOptimization: "15 min early arrival"
    }
  ];

  const coldStorageData = [
    {
      unit: "Cold Storage A",
      temperature: 4.2,
      targetTemp: 4.0,
      humidity: 85,
      targetHumidity: 80,
      capacity: 5000,
      current: 4200,
      products: "Milk, Curd, Paneer",
      efficiency: 94.5,
      energySaving: "12% saved via AI scheduling"
    },
    {
      unit: "Cold Storage B",
      temperature: 6.1,
      targetTemp: 4.0,
      humidity: 88,
      targetHumidity: 80,
      capacity: 3000,  
      current: 2800,
      products: "Butter, Ghee",
      efficiency: 87.2,
      energySaving: "Alert: Temperature high"
    },
    {
      unit: "Freezer Unit",
      temperature: -18.5,
      targetTemp: -18.0,
      humidity: 65,
      targetHumidity: 65,
      capacity: 2000,
      current: 1500,
      products: "Frozen products",
      efficiency: 96.8,
      energySaving: "Optimal performance"
    }
  ];

  const demandForecast = [
    {
      product: "Milk",
      current: 15800,
      forecasted: 18200,
      change: "+15.2%",
      reason: "Festival season approaching",
      recommendation: "Increase production by 20%"
    },
    {
      product: "Ghee", 
      current: 850,
      forecasted: 1200,
      change: "+41.2%",
      reason: "Diwali demand spike",
      recommendation: "Prepare additional packaging"
    },
    {
      product: "Curd",
      current: 3200,
      forecasted: 3400,
      change: "+6.3%",
      reason: "Seasonal increase",
      recommendation: "Maintain current levels"
    },
    {
      product: "Paneer",
      current: 1200,
      forecasted: 1100,
      change: "-8.3%",
      reason: "Market saturation",
      recommendation: "Optimize inventory"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in-transit': return 'bg-blue-100 text-blue-800';
      case 'loading': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTempColor = (temp: number, target: number) => {
    const diff = Math.abs(temp - target);
    if (diff <= 0.5) return 'text-green-600';
    if (diff <= 1.0) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Chart Data
  const deliveryPerformanceData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'On-Time Deliveries (%)',
        data: [94, 96, 92, 95, 97, 89, 91],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target (%)',
        data: [95, 95, 95, 95, 95, 95, 95],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const routeEfficiencyData = {
    labels: ['Route A', 'Route B', 'Route C', 'Route D', 'Route E'],
    datasets: [
      {
        label: 'Fuel Efficiency (km/L)',
        data: [12.5, 11.8, 13.2, 10.9, 12.1],
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
      {
        label: 'AI Optimized (km/L)',
        data: [14.8, 13.9, 15.1, 12.8, 14.3],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
      },
    ],
  };

  const coldChainTempData = {
    labels: ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM', '12 AM', '3 AM'],
    datasets: [
      {
        label: 'Cold Storage 1 (°C)',
        data: [4.2, 4.1, 4.3, 4.0, 4.2, 4.1, 4.0, 4.2],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Cold Storage 2 (°C)',
        data: [3.8, 3.9, 4.0, 3.7, 3.9, 3.8, 3.9, 4.0],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Target Range (°C)',
        data: [4, 4, 4, 4, 4, 4, 4, 4],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderDash: [5, 5],
        tension: 0.4,
      }
    ],
  };

  const vehicleStatusData = {
    labels: ['In Transit', 'Loading', 'Completed', 'Delayed', 'Maintenance'],
    datasets: [
      {
        data: [35, 20, 30, 10, 5],
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(245, 158, 11)',
          'rgb(34, 197, 94)',
          'rgb(239, 68, 68)',
          'rgb(156, 163, 175)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header with View Selection */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Dispatch & Cold Chain</h2>
          <p className="text-sm text-gray-600">AI-optimized logistics and DCS collection management</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="overview">Overview</option>
            <option value="dcs-dispatch">DCS Dispatch</option>
            <option value="route-optimization">Route Optimization</option>
          </select>
          <select
            value={selectedView}
            onChange={(e) => setSelectedView(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm"
          >
            <option value="dispatch">Dispatch Operations</option>
            <option value="cold-storage">Cold Storage</option>
            <option value="forecast">Demand Forecast</option>
          </select>
        </div>
      </div>

      {/* DCS Filters */}
      <DCSFilters
        selectedTaluka={selectedTaluka}
        selectedVillage={selectedVillage}
        selectedPerformance={selectedPerformance}
        onTalukaChange={setSelectedTaluka}
        onVillageChange={setSelectedVillage}
        onPerformanceChange={setSelectedPerformance}
        onClearFilters={handleClearFilters}
        showStats={true}
      />

      {selectedView === "dispatch" && (
        <>
          {/* AI Optimization Insights */}
          <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-600" />
                <span>AI Dispatch & Cold Chain Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <Route className="h-5 w-5 text-green-500 mt-1" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">Route Optimization Success</h4>
                      <p className="text-sm text-gray-700 mb-3">AI reduced fuel consumption by 18% through dynamic route planning. Average delivery time improved by 15 minutes.</p>
                      <Badge className="bg-green-100 text-green-700">High Impact</Badge>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-4 rounded-lg border border-purple-100">
                  <div className="flex items-start space-x-3">
                    <Thermometer className="h-5 w-5 text-blue-500 mt-1" />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">Cold Chain Prediction</h4>
                      <p className="text-sm text-gray-700 mb-3">Predictive maintenance identified potential compressor issues 48 hours in advance, preventing quality loss.</p>
                      <Badge className="bg-blue-100 text-blue-700">Preventive</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dispatch Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">8</div>
                <p className="text-sm text-gray-600">Active Vehicles</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">12.3</div>
                <p className="text-sm text-gray-600">Avg Fuel Efficiency</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600 mb-1">18%</div>
                <p className="text-sm text-gray-600">Fuel Savings (AI)</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">96.5%</div>
                <p className="text-sm text-gray-600">On-time Delivery</p>
              </CardContent>
            </Card>
          </div>

          {/* Vehicle Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Truck className="h-5 w-5 text-blue-600" />
                <span>Real-time Vehicle Tracking</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dispatchData.map((vehicle, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 p-3 rounded-lg">
                        <Truck className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{vehicle.vehicle}</h4>
                        <p className="text-sm text-gray-600">Driver: {vehicle.driver}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <MapPin className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-700">{vehicle.route}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-center">
                      <Badge className={getStatusColor(vehicle.status)}>
                        {vehicle.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">{vehicle.products}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>Departure: {vehicle.departure}</span>
                        <span>Return: {vehicle.expectedReturn}</span>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm text-gray-600">Fuel Efficiency</div>
                      <div className="font-semibold text-green-600">{vehicle.fuelEfficiency} km/L</div>
                      <div className="text-xs text-blue-600 mt-1">{vehicle.aiOptimization}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {selectedView === "cold-storage" && (
        <>
          {/* Cold Storage Monitoring */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Snowflake className="h-5 w-5 text-cyan-600" />
                <span>Cold Storage Monitoring</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {coldStorageData.map((storage, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-cyan-100 p-2 rounded-lg">
                          <Snowflake className="h-5 w-5 text-cyan-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{storage.unit}</h3>
                          <p className="text-sm text-gray-600">{storage.products}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Thermometer className="h-4 w-4 text-blue-500" />
                          <span className="text-sm text-gray-600">Temperature</span>
                        </div>
                        <span className={`font-medium ${getTempColor(storage.temperature, storage.targetTemp)}`}>
                          {storage.temperature}°C / {storage.targetTemp}°C
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Humidity</span>
                        <span className="font-medium text-gray-900">
                          {storage.humidity}% / {storage.targetHumidity}%
                        </span>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600">Capacity</span>
                          <span>{storage.current}kg / {storage.capacity}kg</span>
                        </div>
                        <Progress value={(storage.current / storage.capacity) * 100} className="h-2" />
                      </div>
                    </div>

                    <div className="bg-purple-50 p-3 rounded-lg">
                      <div className="flex items-center space-x-2 mb-1">
                        <Brain className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-900">AI Optimization</span>
                      </div>
                      <p className="text-sm text-purple-800">{storage.energySaving}</p>
                      <p className="text-xs text-purple-600 mt-1">Efficiency: {storage.efficiency}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {selectedView === "forecast" && (
        <>
          {/* SKU-wise Demand Forecasting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-green-600" />
                <span>SKU-wise Demand Forecasting</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demandForecast.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="bg-green-100 p-3 rounded-lg">
                        <Package className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{item.product}</h4>
                        <p className="text-sm text-gray-600">{item.reason}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Current</p>
                        <p className="font-semibold text-gray-900">{item.current.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Forecasted</p>
                        <p className="font-semibold text-blue-600">{item.forecasted.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Change</p>
                        <p className={`font-semibold ${
                          item.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {item.change}
                        </p>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="bg-blue-50 p-2 rounded">
                        <p className="text-sm font-medium text-blue-900">AI Recommendation</p>
                        <p className="text-xs text-blue-800">{item.recommendation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Dispatch & Cold Chain Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Delivery Performance Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span>Weekly Delivery Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={deliveryPerformanceData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Route Efficiency Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Route className="h-5 w-5 text-blue-600" />
              <span>Route Efficiency: Before vs AI Optimized</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Bar data={routeEfficiencyData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cold Chain & Vehicle Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cold Chain Temperature Monitoring */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Thermometer className="h-5 w-5 text-blue-600" />
              <span>Cold Chain Temperature Monitoring</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Line data={coldChainTempData} options={chartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Truck className="h-5 w-5 text-purple-600" />
              <span>Vehicle Status Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Doughnut data={vehicleStatusData} options={pieChartOptions} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* DCS Dispatch Analysis */}
      {viewMode === 'dcs-dispatch' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* DCS Selector */}
          <div className="lg:col-span-1">
            <DCSSelector
              selectedDCS={selectedDCS}
              onDCSSelect={(dcs) => setSelectedDCS(dcs.id)}
              showDetails={true}
              maxHeight="600px"
              filters={{
                taluka: selectedTaluka,
                village: selectedVillage,
                performance: selectedPerformance
              }}
            />
          </div>

          {/* DCS Dispatch Details */}
          <div className="lg:col-span-2 space-y-4">
            {getDCSDispatchMetrics().slice(0, 6).map((dcs) => (
              <Card key={dcs.id} className={`border-2 ${
                selectedDCS === dcs.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{dcs.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className="bg-green-100 text-green-700">
                          <MapPin className="h-3 w-3 mr-1" />
                          {dcs.village}, {dcs.taluka}
                        </Badge>
                        <Badge className={
                          dcs.vehicle.status === 'operational' ? 'bg-green-100 text-green-700' :
                          'bg-red-100 text-red-700'
                        }>
                          {dcs.vehicle.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{dcs.logistics.onTimeDelivery}%</div>
                      <div className="text-xs text-gray-600">On-Time Delivery</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Dispatch Schedule */}
                  <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">Dispatch Schedule</span>
                      </div>
                      <Badge className="bg-blue-100 text-blue-700">
                        {dcs.dispatch.vehicleNumber}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-blue-600">Collection:</span>
                        <span className="font-medium ml-2">{dcs.dispatch.collectionTime}</span>
                      </div>
                      <div>
                        <span className="text-blue-600">Departure:</span>
                        <span className="font-medium ml-2">{dcs.dispatch.departureTime}</span>
                      </div>
                      <div>
                        <span className="text-blue-600">Arrival:</span>
                        <span className="font-medium ml-2">{dcs.dispatch.estimatedArrival}</span>
                      </div>
                    </div>
                  </div>

                  {/* Logistics Metrics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-blue-50 p-3 rounded-lg text-center">
                      <Milk className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-blue-900">{dcs.dailyCollection}L</div>
                      <div className="text-xs text-blue-600">Daily Collection</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg text-center">
                      <Navigation className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-green-900">{dcs.dispatch.distance}km</div>
                      <div className="text-xs text-green-600">Distance</div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg text-center">
                      <Fuel className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-purple-900">{dcs.logistics.fuelEfficiency}km/L</div>
                      <div className="text-xs text-purple-600">Fuel Efficiency</div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg text-center">
                      <Truck className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                      <div className="text-sm font-medium text-orange-900">{dcs.logistics.vehicleUtilization}%</div>
                      <div className="text-xs text-orange-600">Utilization</div>
                    </div>
                  </div>

                  {/* Vehicle Status */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-2">Vehicle Status</div>
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-gray-50 p-2 rounded text-center">
                        <div className="text-xs text-gray-600">Temperature</div>
                        <div className="text-sm font-medium text-gray-900">{dcs.vehicle.temperature}°C</div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded text-center">
                        <div className="text-xs text-gray-600">Fuel Level</div>
                        <div className="text-sm font-medium text-gray-900">{dcs.vehicle.fuelLevel}%</div>
                      </div>
                      <div className="bg-gray-50 p-2 rounded text-center">
                        <div className="text-xs text-gray-600">GPS</div>
                        <div className="text-sm font-medium text-green-700">Active</div>
                      </div>
                    </div>
                  </div>

                  {/* Images */}
                  {dcs.images && dcs.images.length > 0 && (
                    <div className="mb-4">
                      <div className="text-sm font-medium text-gray-700 mb-2">Dispatch Vehicle Images</div>
                      <div className="flex space-x-2 overflow-x-auto">
                        {dcs.images.slice(0, 4).map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Dispatch Vehicle ${index + 1}`}
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200 flex-shrink-0"
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* AI Insights */}
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div className="flex items-start space-x-2">
                      <Brain className="h-4 w-4 text-purple-600 mt-0.5" />
                      <div>
                        <div className="text-sm font-medium text-purple-900 mb-1">AI Logistics Insight</div>
                        <div className="text-sm text-purple-700 mb-2">{dcs.aiInsights.prediction}</div>
                        <div className="text-xs text-purple-600">
                          Recommendation: {dcs.aiInsights.recommendation}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Route Optimization */}
      {viewMode === 'route-optimization' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Route className="h-5 w-5 text-blue-600" />
              <span>DCS Route Optimization Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {['excellent', 'good', 'average', 'poor'].map((performance) => {
                const dcsInCategory = getDCSDispatchMetrics().filter(dcs => dcs.performance === performance);
                const avgEfficiency = dcsInCategory.reduce((sum, dcs) => sum + dcs.logistics.routeOptimization, 0) / dcsInCategory.length || 0;
                return (
                  <div key={performance} className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{avgEfficiency.toFixed(1)}%</div>
                      <div className="text-sm text-blue-700 capitalize">{performance} DCS Routes</div>
                      <div className="text-xs text-gray-600">{dcsInCategory.length} DCS</div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 px-4 py-2 text-left">DCS Name</th>
                    <th className="border border-gray-200 px-4 py-2 text-left">Route</th>
                    <th className="border border-gray-200 px-4 py-2 text-right">Distance</th>
                    <th className="border border-gray-200 px-4 py-2 text-right">Fuel Efficiency</th>
                    <th className="border border-gray-200 px-4 py-2 text-center">On-Time %</th>
                    <th className="border border-gray-200 px-4 py-2 text-center">Optimization</th>
                    <th className="border border-gray-200 px-4 py-2 text-right">Cost/L</th>
                  </tr>
                </thead>
                <tbody>
                  {getDCSDispatchMetrics().map((dcs) => (
                    <tr key={dcs.id} className="hover:bg-gray-50">
                      <td className="border border-gray-200 px-4 py-2 font-medium">{dcs.name}</td>
                      <td className="border border-gray-200 px-4 py-2">{dcs.dispatch.route}</td>
                      <td className="border border-gray-200 px-4 py-2 text-right">{dcs.dispatch.distance}km</td>
                      <td className="border border-gray-200 px-4 py-2 text-right">{dcs.logistics.fuelEfficiency}km/L</td>
                      <td className="border border-gray-200 px-4 py-2 text-center">{dcs.logistics.onTimeDelivery}%</td>
                      <td className="border border-gray-200 px-4 py-2 text-center">
                        <Badge className={
                          dcs.logistics.routeOptimization >= 90 ? 'bg-green-100 text-green-700' :
                          dcs.logistics.routeOptimization >= 80 ? 'bg-blue-100 text-blue-700' :
                          dcs.logistics.routeOptimization >= 70 ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }>
                          {dcs.logistics.routeOptimization}%
                        </Badge>
                      </td>
                      <td className="border border-gray-200 px-4 py-2 text-right">₹{dcs.logistics.costPerLiter}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DispatchDepartment;
