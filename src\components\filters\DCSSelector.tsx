import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Building, 
  Search, 
  MapPin, 
  Users, 
  Milk,
  TrendingUp,
  Star,
  Phone,
  Calendar
} from "lucide-react";
import { dcsData, DCSData, filterDCSData } from "@/data/dcsData";

interface DCSSelectorProps {
  selectedDCS?: string;
  onDCSSelect?: (dcs: DCSData) => void;
  showDetails?: boolean;
  maxHeight?: string;
  filters?: {
    taluka?: string;
    village?: string;
    performance?: string;
  };
}

const DCSSelector = ({
  selectedDCS,
  onDCSSelect,
  showDetails = true,
  maxHeight = "400px",
  filters = {}
}: DCSSelectorProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredDCS, setFilteredDCS] = useState<DCSData[]>(dcsData);

  useEffect(() => {
    let filtered = filterDCSData(filters);
    
    if (searchTerm) {
      filtered = filtered.filter(dcs => 
        dcs.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dcs.village.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dcs.taluka.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dcs.contactPerson.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    setFilteredDCS(filtered);
  }, [searchTerm, filters]);

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'bg-green-100 text-green-700 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'average': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'poor': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return '🌟';
      case 'good': return '👍';
      case 'average': return '⚡';
      case 'poor': return '⚠️';
      default: return '📊';
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <Building className="h-5 w-5 text-blue-600" />
          <span>DCS Selection</span>
          <Badge className="bg-blue-100 text-blue-700">
            {filteredDCS.length} DCS
          </Badge>
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search DCS by name, village, or contact person..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div 
          className="space-y-2 px-4 pb-4 overflow-y-auto"
          style={{ maxHeight }}
        >
          {filteredDCS.map((dcs) => (
            <Card
              key={dcs.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-md border-2 ${
                selectedDCS === dcs.id 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-blue-300'
              }`}
              onClick={() => onDCSSelect?.(dcs)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                      {dcs.name}
                    </h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <MapPin className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">
                        {dcs.village}, {dcs.taluka}
                      </span>
                    </div>
                  </div>
                  <Badge className={`text-xs ${getPerformanceColor(dcs.performance)}`}>
                    {getPerformanceIcon(dcs.performance)} {dcs.performance}
                  </Badge>
                </div>

                {showDetails && (
                  <div className="grid grid-cols-2 gap-3 mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center space-x-2">
                      <Users className="h-3 w-3 text-blue-500" />
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          {dcs.activeFarmers}
                        </div>
                        <div className="text-xs text-gray-500">Farmers</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Milk className="h-3 w-3 text-green-500" />
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          {dcs.dailyMilkCollection}L
                        </div>
                        <div className="text-xs text-gray-500">Daily</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Star className="h-3 w-3 text-yellow-500" />
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          {dcs.qualityScore}%
                        </div>
                        <div className="text-xs text-gray-500">Quality</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-3 w-3 text-purple-500" />
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          ₹{(dcs.monthlyRevenue / 100000).toFixed(1)}L
                        </div>
                        <div className="text-xs text-gray-500">Revenue</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Contact Info */}
                <div className="mt-3 pt-2 border-t border-gray-100">
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-1">
                      <Phone className="h-3 w-3 text-gray-400" />
                      <span className="text-gray-600">{dcs.contactPerson}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-gray-600">Est. {dcs.establishedYear}</span>
                    </div>
                  </div>
                </div>

                {/* AI Insights Preview */}
                {dcs.aiInsights && (
                  <div className="mt-2 p-2 bg-purple-50 rounded-lg border border-purple-100">
                    <div className="flex items-start space-x-2">
                      <div className="text-xs">🤖</div>
                      <div className="flex-1">
                        <div className="text-xs font-medium text-purple-700 mb-1">
                          AI Insight
                        </div>
                        <div className="text-xs text-purple-600 leading-relaxed">
                          {dcs.aiInsights.prediction.length > 60 
                            ? `${dcs.aiInsights.prediction.substring(0, 60)}...`
                            : dcs.aiInsights.prediction
                          }
                        </div>
                        <div className="flex items-center justify-between mt-1">
                          <Badge className={`text-xs ${
                            dcs.aiInsights.riskLevel === 'low' ? 'bg-green-100 text-green-600' :
                            dcs.aiInsights.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {dcs.aiInsights.riskLevel} risk
                          </Badge>
                          <span className="text-xs text-purple-500">
                            {dcs.aiInsights.confidence}% confidence
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {filteredDCS.length === 0 && (
            <div className="text-center py-8">
              <Building className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500 text-sm">No DCS found matching your criteria</p>
              <p className="text-gray-400 text-xs mt-1">Try adjusting your search or filters</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DCSSelector;
